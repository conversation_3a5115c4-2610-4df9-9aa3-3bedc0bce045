const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    default: null
  },
  location: {
    type: String,
    required: true,
    trim: true
  },
  image: {
    type: String, // Path to the main/cover image file
    default: null
  },
  gallery: [{
    type: String // Array of paths to gallery images
  }],
  organizer: {
    type: String,
    default: 'Science Club'
  },
  registrationLink: {
    type: String,
    default: null
  },
  isPast: {
    type: Boolean,
    default: false
  },
  featured: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
eventSchema.pre('save', function(next) {
  this.updatedAt = Date.now();

  // Check if the event is in the past
  const currentDate = new Date();
  if (this.date < currentDate) {
    this.isPast = true;
  }

  next();
});

const Event = mongoose.model('Event', eventSchema);

module.exports = Event;
