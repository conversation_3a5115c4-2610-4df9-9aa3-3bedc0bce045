/**
 * Debug Logger Middleware
 * Logs detailed information about requests for debugging production issues
 */

const debugLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log incoming request
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);
  console.log(`  Headers: ${JSON.stringify(req.headers, null, 2)}`);
  console.log(`  Query: ${JSON.stringify(req.query, null, 2)}`);
  console.log(`  Body: ${JSON.stringify(req.body, null, 2)}`);
  
  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] Response ${res.statusCode} in ${duration}ms`);
    console.log(`  Data: ${JSON.stringify(data, null, 2)}`);
    return originalJson.call(this, data);
  };
  
  next();
};

module.exports = debugLogger;
