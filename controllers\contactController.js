const apiClient = require('../utils/apiClient');
const { Contact } = require('../models');

// Helper function to get auth token from session
const getAuthToken = (req) => {
  return req.session.authToken || req.session.user?.token;
};

// Display contact form
exports.showContactForm = (req, res) => {
  res.render('pages/contact/index', {
    title: 'Contact Us',
    currentPage: 'contact',
    formData: {},
    errors: {},
    success: false,
    successMessage: ''
  });
};

// Handle contact form submission
exports.submitContact = async (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;

    // Submit contact via API
    const response = await apiClient.submitContact({
      name,
      email,
      phone,
      subject: subject || 'Contact Form Submission',
      message
    });

    if (!response.success) {
      throw new Error(response.message);
    }

    // Return success response with empty form data
    res.render('pages/contact/index', {
      title: 'Contact Us',
      currentPage: 'contact',
      formData: {}, // Clear form data
      errors: {},
      success: true,
      successMessage: 'Your message has been sent successfully! We\'ll get back to you soon.'
    });

  } catch (error) {
    console.error('Contact form error:', error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const errors = {};
      Object.keys(error.errors).forEach(key => {
        errors[key] = error.errors[key].message;
      });

      return res.render('pages/contact/index', {
        title: 'Contact Us',
        currentPage: 'contact',
        formData: req.body,
        errors,
        success: false
      });
    }

    // Handle API and other errors
    let errorMessage = 'Something went wrong. Please try again.';

    // Check if it's an API error with specific message
    if (error.response && error.response.data && error.response.data.message) {
      errorMessage = error.response.data.message;
    } else if (error.message && error.message.includes('API')) {
      errorMessage = 'Unable to submit your message at this time. Please try again later.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.render('pages/contact/index', {
      title: 'Contact Us',
      currentPage: 'contact',
      formData: req.body,
      errors: { general: errorMessage },
      success: false
    });
  }
};



// Admin: Get all contacts
exports.getContacts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const skip = (page - 1) * limit;

    const contacts = await Contact.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Contact.countDocuments();
    const totalPages = Math.ceil(total / limit);

    res.render('admin/contacts', {
      title: 'Contact Messages',
      contacts,
      currentPage: page,
      totalPages,
      total,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching contacts:', error);
    req.flash('error', 'Error loading contact messages');
    res.redirect('/admin/dashboard');
  }
};

// Admin: View single contact
exports.viewContact = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);

    if (!contact) {
      req.flash('error', 'Contact message not found');
      return res.redirect('/admin/contacts');
    }

    // Mark as read
    if (!contact.isRead) {
      contact.isRead = true;
      contact.readAt = new Date();
      await contact.save();
    }

    res.render('admin/contact-view', {
      title: 'Contact Message',
      contact,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error viewing contact:', error);
    req.flash('error', 'Error loading contact message');
    res.redirect('/admin/contacts');
  }
};

// Admin: Delete contact
exports.deleteContact = async (req, res) => {
  try {
    await Contact.findByIdAndDelete(req.params.id);
    req.flash('success', 'Contact message deleted successfully');
    res.redirect('/admin/contacts');
  } catch (error) {
    console.error('Error deleting contact:', error);
    req.flash('error', 'Error deleting contact message');
    res.redirect('/admin/contacts');
  }
};

// Admin: Toggle read status
exports.toggleRead = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);

    if (!contact) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    contact.isRead = !contact.isRead;
    contact.readAt = contact.isRead ? new Date() : null;
    await contact.save();

    res.json({
      success: true,
      isRead: contact.isRead,
      readAt: contact.readAt
    });
  } catch (error) {
    console.error('Error toggling read status:', error);
    res.status(500).json({ error: 'Server error' });
  }
};
