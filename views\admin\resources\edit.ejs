<!-- <PERSON>er -->
<div class="flex items-center justify-between mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Edit Resource</h1>
        <p class="text-gray-600 mt-2">Update resource information</p>
    </div>
    <div class="flex space-x-4">
        <a href="/admin/resources" class="btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to Resources
        </a>
        <button onclick="deleteResource('<%= resource._id %>')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-trash mr-2"></i>Delete
        </button>
    </div>
</div>

<!-- Edit Resource Form -->
<div class="bg-white rounded-lg shadow-md">
    <form action="/admin/resources/<%= resource._id %>/edit" method="POST" enctype="multipart/form-data" class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Title <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="title"
                        name="title"
                        value="<%= resource.title %>"
                        required
                        class="form-input w-full"
                        placeholder="Enter resource title"
                    >
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description <span class="text-red-500">*</span>
                    </label>
                    <textarea
                        id="description"
                        name="description"
                        rows="4"
                        required
                        class="form-textarea w-full"
                        placeholder="Enter resource description"
                    ><%= resource.description %></textarea>
                </div>

                <!-- Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                        Resource Type <span class="text-red-500">*</span>
                    </label>
                    <select id="type" name="type" required class="form-select w-full">
                        <option value="">Select resource type</option>
                        <option value="pdf" <%= resource.type === 'pdf' ? 'selected' : '' %>>PDF File</option>
                        <option value="link" <%= resource.type === 'link' ? 'selected' : '' %>>External Link</option>
                        <option value="video" <%= resource.type === 'video' ? 'selected' : '' %>>Video</option>
                    </select>
                </div>

                <!-- URL/File Upload -->
                <div id="url-field" style="<%= resource.type === 'pdf' ? 'display: none;' : '' %>">
                    <label for="url" class="block text-sm font-medium text-gray-700 mb-2">
                        URL <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="url"
                        id="url"
                        name="url"
                        value="<%= resource.type !== 'pdf' ? resource.url : '' %>"
                        class="form-input w-full"
                        placeholder="https://example.com/resource"
                        <%= resource.type !== 'pdf' ? 'required' : '' %>
                    >
                </div>

                <div id="file-field" style="<%= resource.type !== 'pdf' ? 'display: none;' : '' %>">
                    <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                        PDF File
                    </label>
                    <% if (resource.type === 'pdf' && resource.filePath) { %>
                        <div class="mb-3 p-3 bg-gray-50 rounded-lg">
                            <p class="text-sm text-gray-600 mb-2">Current file:</p>
                            <a href="<%= resource.filePath %>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-file-pdf mr-1"></i>View current PDF
                            </a>
                        </div>
                    <% } %>
                    <input
                        type="file"
                        id="file"
                        name="file"
                        accept=".pdf"
                        class="form-input w-full"
                    >
                    <p class="text-sm text-gray-500 mt-1">
                        Maximum file size: 20MB. Only PDF files are allowed. 
                        <% if (resource.type === 'pdf') { %>Leave empty to keep current file.<% } %>
                    </p>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select id="category" name="category" required class="form-select w-full">
                        <option value="">Select category</option>
                        <option value="Research Papers" <%= resource.category === 'Research Papers' ? 'selected' : '' %>>Research Papers</option>
                        <option value="Tools" <%= resource.category === 'Tools' ? 'selected' : '' %>>Tools</option>
                        <option value="Datasets" <%= resource.category === 'Datasets' ? 'selected' : '' %>>Datasets</option>
                        <option value="Tutorials" <%= resource.category === 'Tutorials' ? 'selected' : '' %>>Tutorials</option>
                        <option value="Software" <%= resource.category === 'Software' ? 'selected' : '' %>>Software</option>
                        <option value="Documentation" <%= resource.category === 'Documentation' ? 'selected' : '' %>>Documentation</option>
                        <option value="Templates" <%= resource.category === 'Templates' ? 'selected' : '' %>>Templates</option>
                        <option value="Guides" <%= resource.category === 'Guides' ? 'selected' : '' %>>Guides</option>
                    </select>
                </div>

                <!-- Tags -->
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                        Tags
                    </label>
                    <input
                        type="text"
                        id="tags"
                        name="tags"
                        value="<%= resource.tags ? resource.tags.join(', ') : '' %>"
                        class="form-input w-full"
                        placeholder="Enter tags separated by commas"
                    >
                    <p class="text-sm text-gray-500 mt-1">Separate multiple tags with commas (e.g., AI, Machine Learning, Python)</p>
                </div>

                <!-- Featured -->
                <div>
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="featured"
                            name="featured"
                            <%= resource.featured ? 'checked' : '' %>
                            class="form-checkbox h-4 w-4 text-green-600"
                        >
                        <label for="featured" class="ml-2 block text-sm text-gray-700">
                            Mark as featured resource
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">Featured resources will be highlighted on the resources page</p>
                </div>

                <!-- Resource Info -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Resource Information</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>Created:</span>
                            <span><%= new Date(resource.createdAt).toLocaleDateString() %></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Last Updated:</span>
                            <span><%= new Date(resource.updatedAt).toLocaleDateString() %></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Resource ID:</span>
                            <span class="font-mono text-xs"><%= resource._id %></span>
                        </div>
                    </div>
                </div>

                <!-- Preview Section -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Preview</h3>
                    <div class="bg-white rounded-lg border p-4">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-file text-gray-400" id="preview-icon"></i>
                            </div>
                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full" id="preview-type">
                                Select type
                            </span>
                        </div>
                        <h4 class="font-medium text-gray-900 mb-2" id="preview-title">Resource Title</h4>
                        <p class="text-gray-600 text-sm mb-3" id="preview-description">Resource description will appear here...</p>
                        <div class="flex items-center justify-between">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full" id="preview-category">
                                Category
                            </span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full" id="preview-featured" style="display: none;">
                                <i class="fas fa-star mr-1"></i>Featured
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t">
            <a href="/admin/resources" class="btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn-primary">
                <i class="fas fa-save mr-2"></i>Update Resource
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const urlField = document.getElementById('url-field');
    const fileField = document.getElementById('file-field');
    const urlInput = document.getElementById('url');
    const fileInput = document.getElementById('file');
    
    // Preview elements
    const previewIcon = document.getElementById('preview-icon');
    const previewType = document.getElementById('preview-type');
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const previewCategory = document.getElementById('preview-category');
    const previewFeatured = document.getElementById('preview-featured');
    
    // Form inputs
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const categorySelect = document.getElementById('category');
    const featuredCheckbox = document.getElementById('featured');

    // Initialize preview
    updatePreview();

    // Handle type change
    typeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        
        if (selectedType === 'pdf') {
            urlField.style.display = 'none';
            fileField.style.display = 'block';
            urlInput.removeAttribute('required');
        } else {
            urlField.style.display = 'block';
            fileField.style.display = 'none';
            urlInput.setAttribute('required', 'required');
        }
        
        updatePreview();
    });

    // Update preview
    function updatePreview() {
        const type = typeSelect.value;
        const title = titleInput.value || 'Resource Title';
        const description = descriptionInput.value || 'Resource description will appear here...';
        const category = categorySelect.value || 'Category';
        const featured = featuredCheckbox.checked;
        
        // Update icon and type
        if (type === 'pdf') {
            previewIcon.className = 'fas fa-file-pdf text-red-600';
            previewType.textContent = 'PDF';
            previewType.className = 'bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full';
        } else if (type === 'video') {
            previewIcon.className = 'fas fa-play-circle text-blue-600';
            previewType.textContent = 'VIDEO';
            previewType.className = 'bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full';
        } else if (type === 'link') {
            previewIcon.className = 'fas fa-external-link-alt text-green-600';
            previewType.textContent = 'LINK';
            previewType.className = 'bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full';
        } else {
            previewIcon.className = 'fas fa-file text-gray-400';
            previewType.textContent = 'Select type';
            previewType.className = 'bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full';
        }
        
        previewTitle.textContent = title;
        previewDescription.textContent = description.length > 100 ? description.substring(0, 100) + '...' : description;
        previewCategory.textContent = category;
        previewFeatured.style.display = featured ? 'inline-flex' : 'none';
    }

    // Add event listeners for preview updates
    titleInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    categorySelect.addEventListener('change', updatePreview);
    featuredCheckbox.addEventListener('change', updatePreview);
});

// Delete resource function
function deleteResource(resourceId) {
    if (confirm('Are you sure you want to delete this resource? This action cannot be undone.')) {
        fetch(`/admin/resources/${resourceId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '/admin/resources';
            } else {
                alert('Error deleting resource. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting resource. Please try again.');
        });
    }
}
</script>
