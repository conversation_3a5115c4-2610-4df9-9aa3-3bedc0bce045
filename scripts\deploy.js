#!/usr/bin/env node

/**
 * Deployment script for Science Club website
 * This script helps prepare the application for deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Science Club Deployment Script');
console.log('==================================\n');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'app.js',
  '.env.example',
  'models/index.js'
];

console.log('📋 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please ensure all files are present before deployment.');
  process.exit(1);
}

// Check package.json for required dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  'express',
  'mongoose',
  'ejs',
  'bcrypt',
  'express-session',
  'multer',
  'nodemailer',
  'dotenv'
];

let allDepsPresent = true;
requiredDeps.forEach(dep => {
  if (packageJson.dependencies && packageJson.dependencies[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
    allDepsPresent = false;
  }
});

if (!allDepsPresent) {
  console.log('\n❌ Some required dependencies are missing. Run "npm install" to install them.');
  process.exit(1);
}

// Check if upload directories exist
console.log('\n📁 Checking upload directories...');
const uploadDirs = [
  'public/uploads',
  'public/uploads/projects',
  'public/uploads/blog',
  'public/uploads/resources',
  'public/uploads/events'
];

uploadDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}`);
  } else {
    console.log(`⚠️  ${dir} - Creating...`);
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ ${dir} - Created`);
  }
});

// Create .gitignore if it doesn't exist
console.log('\n📝 Checking .gitignore...');
const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*

# Environment variables
.env

# Uploads (optional - remove if you want to track uploads)
public/uploads/*
!public/uploads/.gitkeep

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
dist/
build/
`;

if (!fs.existsSync('.gitignore')) {
  fs.writeFileSync('.gitignore', gitignoreContent);
  console.log('✅ .gitignore created');
} else {
  console.log('✅ .gitignore exists');
}

// Create .gitkeep files for upload directories
uploadDirs.forEach(dir => {
  const gitkeepPath = path.join(dir, '.gitkeep');
  if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, '');
  }
});

// Check environment configuration
console.log('\n🔧 Environment Configuration Check...');
if (fs.existsSync('.env')) {
  console.log('✅ .env file exists');
  
  // Read .env and check for required variables
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredEnvVars = [
    'MONGODB_URI',
    'SESSION_SECRET',
    'PORT'
  ];
  
  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(`${envVar}=`)) {
      console.log(`✅ ${envVar} configured`);
    } else {
      console.log(`⚠️  ${envVar} not found in .env`);
    }
  });
} else {
  console.log('⚠️  .env file not found. Copy .env.example to .env and configure it.');
}

// Deployment platform specific checks
console.log('\n🌐 Deployment Platform Preparation...');

// Create Procfile for Heroku
const procfileContent = 'web: node app.js\n';
if (!fs.existsSync('Procfile')) {
  fs.writeFileSync('Procfile', procfileContent);
  console.log('✅ Procfile created for Heroku');
} else {
  console.log('✅ Procfile exists');
}

// Create render.yaml for Render.com
const renderYamlContent = `services:
  - type: web
    name: scienceclub
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        sync: false
      - key: SESSION_SECRET
        generateValue: true
      - key: PORT
        value: 10000
`;

if (!fs.existsSync('render.yaml')) {
  fs.writeFileSync('render.yaml', renderYamlContent);
  console.log('✅ render.yaml created for Render.com');
} else {
  console.log('✅ render.yaml exists');
}

// Create railway.json for Railway
const railwayJsonContent = JSON.stringify({
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}, null, 2);

if (!fs.existsSync('railway.json')) {
  fs.writeFileSync('railway.json', railwayJsonContent);
  console.log('✅ railway.json created for Railway');
} else {
  console.log('✅ railway.json exists');
}

// Final deployment checklist
console.log('\n✅ Deployment Preparation Complete!');
console.log('\n📋 Pre-deployment Checklist:');
console.log('   1. ✅ All required files present');
console.log('   2. ✅ Dependencies installed');
console.log('   3. ✅ Upload directories created');
console.log('   4. ✅ Platform-specific files created');

console.log('\n🚀 Next Steps for Deployment:');
console.log('\n📌 For Render.com:');
console.log('   1. Push your code to GitHub');
console.log('   2. Connect your GitHub repo to Render');
console.log('   3. Set environment variables in Render dashboard');
console.log('   4. Deploy!');

console.log('\n📌 For Railway:');
console.log('   1. Install Railway CLI: npm install -g @railway/cli');
console.log('   2. Login: railway login');
console.log('   3. Initialize: railway init');
console.log('   4. Set environment variables: railway variables set');
console.log('   5. Deploy: railway up');

console.log('\n📌 For Heroku:');
console.log('   1. Install Heroku CLI');
console.log('   2. Login: heroku login');
console.log('   3. Create app: heroku create your-app-name');
console.log('   4. Set environment variables: heroku config:set');
console.log('   5. Deploy: git push heroku main');

console.log('\n🔐 Required Environment Variables:');
console.log('   - MONGODB_URI (MongoDB connection string)');
console.log('   - SESSION_SECRET (random secret key)');
console.log('   - NODE_ENV=production');
console.log('   - EMAIL_USER (optional, for contact form)');
console.log('   - EMAIL_PASS (optional, for contact form)');

console.log('\n💡 Tips:');
console.log('   - Use MongoDB Atlas for cloud database');
console.log('   - Generate a strong SESSION_SECRET');
console.log('   - Test locally before deploying');
console.log('   - Run "npm run seed" after first deployment');

console.log('\n🎉 Good luck with your deployment!');
