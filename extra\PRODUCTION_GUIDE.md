# Science Club API - Production Deployment Guide

## 🚀 Production Checklist

### 1. Environment Configuration

#### Required Environment Variables
```env
# Database
MONGODB_URI=mongodb://your-production-db-url

# JWT Security
JWT_SECRET=your-super-secure-production-jwt-secret-key-min-32-chars
JWT_EXPIRES_IN=24h142827

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com,https://api.yourdomain.com

# Email Configuration (for contact forms)
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Admin Configuration
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=secure-admin-password

# Production Settings
NODE_ENV=production
PORT=3000
```

### 2. Security Hardening

#### Update Rate Limits for Production
Edit `middleware/rateLimiter.js`:
```javascript
// Production rate limits (more restrictive)
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per 15 minutes
  message: 'Too many API requests, please try again later'
});

const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 login attempts per 15 minutes
  message: 'Too many authentication attempts, please try again later'
});
```

#### Generate Strong JWT Secret
```bash
# Generate a secure JWT secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

#### Update CORS for Production
```javascript
// In app.js, update CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
```

### 3. Database Optimization

#### Add Database Indexes
```javascript
// Add to your database setup script
db.projects.createIndex({ "title": "text", "description": "text" });
db.blogs.createIndex({ "title": "text", "content": "text" });
db.events.createIndex({ "date": 1, "isPast": 1 });
db.resources.createIndex({ "category": 1, "type": 1 });
db.contacts.createIndex({ "createdAt": -1, "isRead": 1 });
db.users.createIndex({ "email": 1 }, { unique: true });
```

### 4. Monitoring and Logging

#### Add Request Logging
```javascript
// Add to app.js
const morgan = require('morgan');

if (process.env.NODE_ENV === 'production') {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}
```

#### API Analytics Middleware
```javascript
// Create middleware/analytics.js
const analytics = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`API ${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
    
    // Log to your analytics service
    // analytics.track('api_request', {
    //   method: req.method,
    //   endpoint: req.originalUrl,
    //   status: res.statusCode,
    //   duration: duration,
    //   userAgent: req.get('User-Agent')
    // });
  });
  
  next();
};
```

### 5. Error Handling

#### Global Error Handler
```javascript
// Add to app.js after routes
app.use((err, req, res, next) => {
  console.error('API Error:', err);
  
  if (req.originalUrl.startsWith('/api')) {
    const ApiResponse = require('./utils/apiResponse');
    
    if (process.env.NODE_ENV === 'production') {
      return ApiResponse.error(res, 'Internal server error', 500);
    } else {
      return ApiResponse.error(res, err.message, 500, err.stack);
    }
  }
  
  next(err);
});
```

### 6. Performance Optimization

#### Add Response Compression
```bash
npm install compression
```

```javascript
// Add to app.js
const compression = require('compression');
app.use(compression());
```

#### Add Response Caching
```javascript
// Add caching middleware for public endpoints
const cache = (duration) => (req, res, next) => {
  if (req.method === 'GET') {
    res.set('Cache-Control', `public, max-age=${duration}`);
  }
  next();
};

// Apply to public API routes
router.get('/projects', cache(300), ...); // 5 minutes
router.get('/blog', cache(600), ...); // 10 minutes
```

### 7. API Versioning

#### Implement API Versioning
```javascript
// Update routes/api/index.js
const express = require('express');
const router = express.Router();

// Version 1 routes
router.use('/v1', require('./v1'));

// Default to latest version
router.use('/', require('./v1'));

module.exports = router;
```

### 8. Documentation and Testing

#### API Documentation URL
- Production: `https://yourdomain.com/api/docs`
- Staging: `https://staging.yourdomain.com/api/docs`

#### Automated Testing in CI/CD
```yaml
# .github/workflows/api-tests.yml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run test:api
```

### 9. Backup and Recovery

#### Database Backup Strategy
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="$MONGODB_URI" --out="/backups/scienceclub_$DATE"
```

#### API Data Export
```javascript
// Create scripts/export-data.js
const exportData = async () => {
  const projects = await Project.find({});
  const blogs = await Blog.find({});
  const events = await Event.find({});
  
  const backup = {
    timestamp: new Date(),
    projects,
    blogs,
    events
  };
  
  fs.writeFileSync(`backup_${Date.now()}.json`, JSON.stringify(backup, null, 2));
};
```

### 10. Deployment Options

#### Option 1: Traditional VPS/Server
```bash
# Install dependencies
npm install --production

# Start with PM2
npm install -g pm2
pm2 start app.js --name "scienceclub-api"
pm2 startup
pm2 save
```

#### Option 2: Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/scienceclub
    depends_on:
      - mongo
  
  mongo:
    image: mongo:5
    volumes:
      - mongo_data:/data/db
    
volumes:
  mongo_data:
```

#### Option 3: Cloud Platforms
- **Heroku**: Add `Procfile` with `web: npm start`
- **Vercel**: Configure as Node.js API
- **Railway**: Direct deployment from Git
- **DigitalOcean App Platform**: Use app spec

### 11. SSL/HTTPS Configuration

#### Nginx Reverse Proxy
```nginx
server {
    listen 443 ssl;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 12. Monitoring and Alerts

#### Health Check Endpoint
Your API already includes `/api/health` - monitor this endpoint.

#### Uptime Monitoring
- Use services like Pingdom, UptimeRobot, or StatusCake
- Monitor: `/api/health`
- Alert on: Response time > 5s, Status code != 200

#### Error Tracking
```javascript
// Add error tracking service (e.g., Sentry)
const Sentry = require('@sentry/node');

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV
});

app.use(Sentry.Handlers.requestHandler());
app.use(Sentry.Handlers.errorHandler());
```

### 13. API Usage Analytics

#### Track API Usage
```javascript
// Add to middleware/analytics.js
const trackApiUsage = async (req, res, next) => {
  // Track endpoint usage
  await Analytics.create({
    endpoint: req.originalUrl,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    timestamp: new Date()
  });
  
  next();
};
```

### 14. Final Production Checklist

- [ ] Environment variables configured
- [ ] JWT secret is secure (32+ characters)
- [ ] CORS origins are restricted to your domains
- [ ] Rate limits are appropriate for production
- [ ] Database indexes are created
- [ ] SSL certificate is installed
- [ ] Monitoring is set up
- [ ] Backup strategy is implemented
- [ ] Error tracking is configured
- [ ] API documentation is accessible
- [ ] Load testing is completed
- [ ] Security audit is performed

### 🎉 Your API is Production Ready!

Once you've completed this checklist, your Science Club API will be ready for production use with:
- ✅ Enterprise-grade security
- ✅ Performance optimization
- ✅ Comprehensive monitoring
- ✅ Scalable architecture
- ✅ Professional documentation

For ongoing maintenance, regularly update dependencies, monitor performance metrics, and review security practices.
