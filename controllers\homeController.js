const apiClient = require('../utils/apiClient');

// Home page controller
exports.getHomePage = async (req, res) => {
  try {
    // Get featured projects via API
    const featuredProjectsResponse = await apiClient.getFeaturedProjects();
    const featuredProjects = featuredProjectsResponse.success ?
      (featuredProjectsResponse.data.projects || featuredProjectsResponse.data || []).slice(0, 3) : [];



    // Get upcoming events via API
    const upcomingEventsResponse = await apiClient.getUpcomingEvents();
    const upcomingEvents = upcomingEventsResponse.success ?
      (upcomingEventsResponse.data.events || upcomingEventsResponse.data || []).slice(0, 3) : [];

    res.render('pages/home', {
      title: 'Science Club - Home',
      currentPage: 'home',
      featuredProjects,
      upcomingEvents
    });
  } catch (error) {
    console.error('Error in home controller:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'An error occurred while loading the home page.'
    });
  }
};
