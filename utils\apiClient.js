const axios = require('axios');

class ApiClient {
  constructor() {
    // For server-side API calls, always use localhost to avoid external round trips
    // This prevents the server from making HTTP requests to itself via the internet
    const port = process.env.PORT || 3000;
    this.baseURL = `http://localhost:${port}`;
    this.apiPrefix = process.env.API_PREFIX || '/api';

    console.log(`API Client initialized with baseURL: ${this.baseURL}${this.apiPrefix}`);

    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL + this.apiPrefix,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[API Client] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API Client] Request Error:', error.message);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API Client] Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
        return response;
      },
      (error) => {
        const errorMsg = error.response?.data?.message || error.message;
        const status = error.response?.status || 'Unknown';
        console.error(`[API Client] Response Error: ${status} - ${errorMsg}`);
        return Promise.reject(error);
      }
    );
  }

  // Helper method to handle API responses
  async handleRequest(requestFn) {
    try {
      const response = await requestFn();
      return {
        success: true,
        data: response.data.data || response.data,
        message: response.data.message || 'Success'
      };
    } catch (error) {
      const errorDetails = {
        message: error.response?.data?.message || error.message || 'An error occurred',
        status: error.response?.status || 500,
        url: error.config?.url || 'Unknown URL',
        method: error.config?.method?.toUpperCase() || 'Unknown Method'
      };

      console.error(`[API Client] Error ${errorDetails.status}: ${errorDetails.method} ${errorDetails.url} - ${errorDetails.message}`);

      return {
        success: false,
        data: null,
        message: errorDetails.message,
        status: errorDetails.status
      };
    }
  }

  // Projects API
  async getProjects(params = {}) {
    return this.handleRequest(() => this.client.get('/projects', { params }));
  }

  async getProject(id) {
    return this.handleRequest(() => this.client.get(`/projects/${id}`));
  }

  async getFeaturedProjects() {
    return this.handleRequest(() => this.client.get('/projects', { params: { featured: true } }));
  }

  async createProject(data, token) {
    return this.handleRequest(() =>
      this.client.post('/projects', data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async updateProject(id, data, token) {
    return this.handleRequest(() =>
      this.client.put(`/projects/${id}`, data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async deleteProject(id, token) {
    return this.handleRequest(() =>
      this.client.delete(`/projects/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }



  // Events API
  async getEvents(params = {}) {
    return this.handleRequest(() => this.client.get('/events', { params }));
  }

  async getEvent(id) {
    return this.handleRequest(() => this.client.get(`/events/${id}`));
  }

  async getUpcomingEvents() {
    return this.handleRequest(() => this.client.get('/events', { params: { upcoming: true } }));
  }

  async createEvent(data, token) {
    return this.handleRequest(() =>
      this.client.post('/events', data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async updateEvent(id, data, token) {
    return this.handleRequest(() =>
      this.client.put(`/events/${id}`, data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async deleteEvent(id, token) {
    return this.handleRequest(() =>
      this.client.delete(`/events/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  // Resources API
  async getResources(params = {}) {
    return this.handleRequest(() => this.client.get('/resources', { params }));
  }

  async getResource(id) {
    return this.handleRequest(() => this.client.get(`/resources/${id}`));
  }

  async getResourcesByCategory(category) {
    return this.handleRequest(() => this.client.get('/resources', { params: { category } }));
  }

  async createResource(data, token) {
    return this.handleRequest(() =>
      this.client.post('/resources', data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async updateResource(id, data, token) {
    return this.handleRequest(() =>
      this.client.put(`/resources/${id}`, data, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async deleteResource(id, token) {
    return this.handleRequest(() =>
      this.client.delete(`/resources/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  // Contacts API
  async submitContact(data) {
    return this.handleRequest(() => this.client.post('/contacts', data));
  }

  async getContacts(params = {}, token) {
    return this.handleRequest(() =>
      this.client.get('/contacts', {
        params,
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  async getContact(id, token) {
    return this.handleRequest(() =>
      this.client.get(`/contacts/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  // Auth API
  async login(credentials) {
    return this.handleRequest(() => this.client.post('/auth/login', credentials));
  }

  async register(userData) {
    return this.handleRequest(() => this.client.post('/auth/register', userData));
  }

  async verifyToken(token) {
    return this.handleRequest(() =>
      this.client.get('/auth/verify', {
        headers: { Authorization: `Bearer ${token}` }
      })
    );
  }

  // Search API
  async search(query, params = {}) {
    return this.handleRequest(() =>
      this.client.get('/search', {
        params: { q: query, ...params }
      })
    );
  }
}

// Create singleton instance
const apiClient = new ApiClient();

module.exports = apiClient;
