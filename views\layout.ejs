<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/jpeg" href="/images/logo.jpg">
    <link rel="apple-touch-icon" href="/images/logo.jpg">

    <!-- CSS -->
    <link href="/css/style.css" rel="stylesheet">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Meta tags -->
    <meta name="description" content="Science Club - Promoting student research, sharing science news and resources, and connecting with the community through events.">
    <meta name="keywords" content="science, research, education, students, club, events, resources">
    <meta name="author" content="Science Club">

    <!-- Open Graph -->
    <meta property="og:title" content="<%= title %>">
    <meta property="og:description" content="Science Club - Promoting student research and scientific education">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= process.env.BASE_URL || 'http://localhost:3000' %>">
    <meta property="og:image" content="<%= process.env.BASE_URL || 'http://localhost:3000' %>/images/logo.jpg">
    <meta property="og:image:alt" content="Science Club Turbat Logo">

    <!-- Additional head content -->
    <%- typeof additionalHead !== 'undefined' ? additionalHead : '' %>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-NKC07WRJT6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-NKC07WRJT6');
</script>

</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <%- include('partials/header') %>

    <!-- Main Content -->
    <main>
        <%- body %>
    </main>

    <!-- Footer -->
    <%- include('partials/footer') %>

    <!-- Scroll to Top Button -->
    <button id="scrollToTop" class="scroll-to-top" aria-label="Scroll to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>

    <!-- Additional scripts -->
    <%- typeof additionalScripts !== 'undefined' ? additionalScripts : '' %>

    <!-- Global Scroll to Top Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const scrollToTopBtn = document.getElementById('scrollToTop');

        if (scrollToTopBtn) {
            // Scroll to Top Button functionality
            function toggleScrollButton() {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.classList.add('show');
                } else {
                    scrollToTopBtn.classList.remove('show');
                }
            }

            function scrollToTop() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }

            // Show/hide scroll button on scroll
            window.addEventListener('scroll', toggleScrollButton);

            // Scroll to top when button is clicked
            scrollToTopBtn.addEventListener('click', scrollToTop);

            // Keyboard accessibility
            scrollToTopBtn.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    scrollToTop();
                }
            });

            // Initial check
            toggleScrollButton();
        }
    });
    </script>
</body>
</html>
