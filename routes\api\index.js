const express = require('express');
const router = express.Router();
const ApiResponse = require('../../utils/apiResponse');
const { apiLimiter } = require('../../middleware/rateLimiter');

// Apply rate limiting to all API routes
router.use(apiLimiter);

// API Routes
router.use('/auth', require('./auth'));
router.use('/projects', require('./projects'));
router.use('/events', require('./events'));
router.use('/resources', require('./resources'));
router.use('/contacts', require('./contacts'));
router.use('/users', require('./users'));
router.use('/search', require('./search'));

// API Health Check
router.get('/health', (req, res) => {
  ApiResponse.success(res, {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  }, 'API is healthy');
});

// Debug endpoint to check database connectivity and data
router.get('/debug', async (req, res) => {
  try {
    const Project = require('../../models/Project');
    const Event = require('../../models/Event');

    // Check database connection
    const dbState = require('mongoose').connection.readyState;
    const dbStates = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    // Count documents
    const projectCount = await Project.countDocuments();
    const featuredProjectCount = await Project.countDocuments({ featured: true });
    const eventCount = await Event.countDocuments();

    // Get sample data
    const sampleProjects = await Project.find({ featured: true }).limit(3).select('title featured createdAt');

    ApiResponse.success(res, {
      database: {
        state: dbStates[dbState] || 'unknown',
        connected: dbState === 1
      },
      counts: {
        totalProjects: projectCount,
        featuredProjects: featuredProjectCount,
        totalEvents: eventCount
      },
      samples: {
        projects: sampleProjects
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
        mongoUri: process.env.MONGODB_URI ? 'Set' : 'Not Set'
      }
    }, 'Debug information retrieved');

  } catch (error) {
    console.error('Debug endpoint error:', error);
    ApiResponse.error(res, `Debug error: ${error.message}`);
  }
});

// API Documentation endpoint
router.get('/docs', (req, res) => {
  const apiDocs = {
    title: 'Science Club API Documentation',
    version: '1.0.0',
    description: 'REST API for Science Club website',
    baseUrl: `${req.protocol}://${req.get('host')}/api`,
    endpoints: {
      authentication: {
        'POST /api/auth/login': 'Login and get access token',
        'POST /api/auth/refresh': 'Refresh access token',
        'POST /api/auth/logout': 'Logout (invalidate token)'
      },
      projects: {
        'GET /api/projects': 'Get all projects (paginated)',
        'GET /api/projects/:id': 'Get project by ID',
        'POST /api/projects': 'Create new project (auth required)',
        'PUT /api/projects/:id': 'Update project (auth required)',
        'DELETE /api/projects/:id': 'Delete project (auth required)',
        'GET /api/projects/featured': 'Get featured projects'
      },

      events: {
        'GET /api/events': 'Get all events (paginated)',
        'GET /api/events/:id': 'Get event by ID',
        'POST /api/events': 'Create new event (auth required)',
        'PUT /api/events/:id': 'Update event (auth required)',
        'DELETE /api/events/:id': 'Delete event (auth required)',
        'GET /api/events/upcoming': 'Get upcoming events'
      },
      resources: {
        'GET /api/resources': 'Get all resources (paginated)',
        'GET /api/resources/:id': 'Get resource by ID',
        'POST /api/resources': 'Create new resource (auth required)',
        'PUT /api/resources/:id': 'Update resource (auth required)',
        'DELETE /api/resources/:id': 'Delete resource (auth required)',
        'GET /api/resources/category/:category': 'Get resources by category'
      },
      contacts: {
        'GET /api/contacts': 'Get all contacts (admin only)',
        'GET /api/contacts/:id': 'Get contact by ID (admin only)',
        'POST /api/contacts': 'Submit contact form',
        'PATCH /api/contacts/:id/read': 'Mark contact as read (admin only)',
        'DELETE /api/contacts/:id': 'Delete contact (admin only)'
      },
      users: {
        'GET /api/users': 'Get all users (admin only)',
        'GET /api/users/:id': 'Get user by ID (admin only)',
        'POST /api/users': 'Create new user (admin only)',
        'PUT /api/users/:id': 'Update user (admin only)',
        'DELETE /api/users/:id': 'Delete user (admin only)'
      },
      search: {
        'GET /api/search': 'Search across all content types',
        'GET /api/search/projects': 'Search projects only',
        'GET /api/search/events': 'Search events only',
        'GET /api/search/resources': 'Search resources only'
      }
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      description: 'Include JWT token in Authorization header for protected endpoints'
    },
    responseFormat: {
      success: {
        success: true,
        message: 'Success message',
        data: 'Response data',
        timestamp: 'ISO timestamp'
      },
      error: {
        success: false,
        message: 'Error message',
        errors: 'Detailed error information (optional)',
        timestamp: 'ISO timestamp'
      },
      paginated: {
        success: true,
        message: 'Success message',
        data: 'Array of items',
        pagination: {
          currentPage: 'Current page number',
          totalPages: 'Total number of pages',
          totalItems: 'Total number of items',
          itemsPerPage: 'Items per page',
          hasNextPage: 'Boolean',
          hasPrevPage: 'Boolean'
        },
        timestamp: 'ISO timestamp'
      }
    },
    rateLimits: {
      general: '100 requests per 15 minutes',
      authentication: '5 attempts per 15 minutes',
      contact: '3 submissions per hour',
      search: '30 requests per minute',
      upload: '10 uploads per hour'
    }
  };

  ApiResponse.success(res, apiDocs, 'API Documentation');
});

// 404 handler for API routes
router.use((req, res) => {
  ApiResponse.notFound(res, `API endpoint ${req.originalUrl} not found`);
});

module.exports = router;
