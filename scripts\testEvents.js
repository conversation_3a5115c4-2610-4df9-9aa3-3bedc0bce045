const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { Event } = require('../models');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/scienceclub');
    console.log('MongoDB connected successfully');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

const testEvents = async () => {
  try {
    const currentDate = new Date();
    console.log('Current date:', currentDate.toISOString());
    
    const upcoming = await Event.find({ date: { $gte: currentDate } }).sort({ date: 1 });
    const past = await Event.find({ date: { $lt: currentDate } }).sort({ date: -1 });
    
    console.log('\n=== UPCOMING EVENTS ===');
    console.log(`Found ${upcoming.length} upcoming events:`);
    upcoming.forEach(e => console.log(`- ${e.title} (${e.date.toDateString()})`));
    
    console.log('\n=== PAST EVENTS ===');
    console.log(`Found ${past.length} past events:`);
    past.forEach(e => console.log(`- ${e.title} (${e.date.toDateString()})`));
    
    console.log('\n✅ Events test completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing events:', error);
    process.exit(1);
  }
};

// Run the test
connectDB().then(() => {
  testEvents();
});
