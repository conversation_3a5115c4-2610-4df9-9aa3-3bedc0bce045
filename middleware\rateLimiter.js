const rateLimit = require('express-rate-limit');
const ApiResponse = require('../utils/apiResponse');

/**
 * Create rate limiter with custom error response
 * @param {Object} options - Rate limit options
 * @returns {Function} Rate limiter middleware
 */
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req, res) => {
      return ApiResponse.error(
        res, 
        options.message || 'Too many requests from this IP, please try again later',
        429
      );
    },
    ...options
  };

  return rateLimit(defaultOptions);
};

// General API rate limiter
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
  message: 'Too many API requests, please try again later'
});

// Strict rate limiter for authentication endpoints
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 login attempts per 15 minutes
  message: 'Too many authentication attempts, please try again later',
  skipSuccessfulRequests: true // Don't count successful requests
});

// Contact form rate limiter
const contactLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 contact form submissions per hour
  message: 'Too many contact form submissions, please try again later'
});

// Search rate limiter
const searchLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 search requests per minute
  message: 'Too many search requests, please slow down'
});

// Upload rate limiter
const uploadLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 uploads per hour
  message: 'Too many file uploads, please try again later'
});

// Admin operations rate limiter
const adminLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 50, // 50 admin operations per minute
  message: 'Too many admin operations, please slow down'
});

module.exports = {
  createRateLimiter,
  apiLimiter,
  authLimiter,
  contactLimiter,
  searchLimiter,
  uploadLimiter,
  adminLimiter
};
