#!/usr/bin/env node

/**
 * Production Setup Script
 * Validates environment and ensures the application is ready for production
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

class ProductionSetup {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
    
    switch (type) {
      case 'error':
        console.error(`${prefix} ${message}`);
        this.errors.push(message);
        break;
      case 'warning':
        console.warn(`${prefix} ${message}`);
        this.warnings.push(message);
        break;
      case 'success':
        console.log(`${prefix} ${message}`);
        this.success.push(message);
        break;
      default:
        console.log(`${prefix} ${message}`);
    }
  }

  checkEnvironmentVariables() {
    this.log('Checking environment variables...', 'info');

    const requiredVars = [
      'MONGODB_URI',
      'SESSION_SECRET',
      'JWT_SECRET',
      'NODE_ENV'
    ];

    const recommendedVars = [
      'BASE_URL',
      'EMAIL_USER',
      'EMAIL_PASS',
      'ALLOWED_ORIGINS'
    ];

    // Check required variables
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        this.log(`Missing required environment variable: ${varName}`, 'error');
      } else {
        this.log(`✓ ${varName} is set`, 'success');
      }
    }

    // Check recommended variables
    for (const varName of recommendedVars) {
      if (!process.env[varName]) {
        this.log(`Missing recommended environment variable: ${varName}`, 'warning');
      } else {
        this.log(`✓ ${varName} is set`, 'success');
      }
    }

    // Validate MongoDB URI
    if (process.env.MONGODB_URI) {
      if (process.env.MONGODB_URI.includes('localhost') || process.env.MONGODB_URI.includes('127.0.0.1')) {
        this.log('MongoDB URI contains localhost - this may not work in production', 'warning');
      } else {
        this.log('✓ MongoDB URI appears to be production-ready', 'success');
      }
    }

    // Check NODE_ENV
    if (process.env.NODE_ENV !== 'production') {
      this.log('NODE_ENV is not set to "production"', 'warning');
    } else {
      this.log('✓ NODE_ENV is set to production', 'success');
    }
  }

  checkFileStructure() {
    this.log('Checking file structure...', 'info');

    const requiredFiles = [
      'app.js',
      'package.json',
      'utils/apiClient.js',
      'public/js/api-client.js'
    ];

    const requiredDirs = [
      'routes/api',
      'controllers',
      'models',
      'views',
      'public',
      'middleware'
    ];

    // Check required files
    for (const file of requiredFiles) {
      if (fs.existsSync(path.join(process.cwd(), file))) {
        this.log(`✓ ${file} exists`, 'success');
      } else {
        this.log(`Missing required file: ${file}`, 'error');
      }
    }

    // Check required directories
    for (const dir of requiredDirs) {
      if (fs.existsSync(path.join(process.cwd(), dir))) {
        this.log(`✓ ${dir}/ directory exists`, 'success');
      } else {
        this.log(`Missing required directory: ${dir}/`, 'error');
      }
    }
  }

  checkAPIEndpoints() {
    this.log('Checking API endpoint files...', 'info');

    const apiFiles = [
      'routes/api/index.js',
      'routes/api/projects.js',
      'routes/api/blog.js',
      'routes/api/events.js',
      'routes/api/resources.js',
      'routes/api/contacts.js',
      'routes/api/auth.js',
      'routes/api/search.js'
    ];

    for (const file of apiFiles) {
      if (fs.existsSync(path.join(process.cwd(), file))) {
        this.log(`✓ ${file} exists`, 'success');
      } else {
        this.log(`Missing API file: ${file}`, 'error');
      }
    }
  }

  checkControllers() {
    this.log('Checking controller files...', 'info');

    const controllers = [
      'controllers/homeController.js',
      'controllers/projectController.js',
      'controllers/blogController.js',
      'controllers/eventController.js',
      'controllers/resourceController.js',
      'controllers/contactController.js'
    ];

    for (const controller of controllers) {
      if (fs.existsSync(path.join(process.cwd(), controller))) {
        // Check if controller uses apiClient
        const content = fs.readFileSync(path.join(process.cwd(), controller), 'utf8');
        if (content.includes('require(\'../utils/apiClient\')')) {
          this.log(`✓ ${controller} uses API client`, 'success');
        } else {
          this.log(`${controller} may still use direct database access`, 'warning');
        }
      } else {
        this.log(`Missing controller: ${controller}`, 'error');
      }
    }
  }

  checkPackageJson() {
    this.log('Checking package.json...', 'info');

    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      // Check required dependencies
      const requiredDeps = [
        'express',
        'mongoose',
        'axios',
        'dotenv',
        'cors',
        'helmet'
      ];

      for (const dep of requiredDeps) {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          this.log(`✓ ${dep} dependency found`, 'success');
        } else {
          this.log(`Missing dependency: ${dep}`, 'error');
        }
      }

      // Check start script
      if (packageJson.scripts && packageJson.scripts.start) {
        this.log('✓ Start script found', 'success');
      } else {
        this.log('Missing start script in package.json', 'error');
      }

    } catch (error) {
      this.log('Error reading package.json', 'error');
    }
  }

  generateReport() {
    this.log('\n=== PRODUCTION SETUP REPORT ===', 'info');
    
    this.log(`✓ Successful checks: ${this.success.length}`, 'success');
    
    if (this.warnings.length > 0) {
      this.log(`⚠ Warnings: ${this.warnings.length}`, 'warning');
      this.warnings.forEach(warning => this.log(`  - ${warning}`, 'warning'));
    }
    
    if (this.errors.length > 0) {
      this.log(`✗ Errors: ${this.errors.length}`, 'error');
      this.errors.forEach(error => this.log(`  - ${error}`, 'error'));
    }

    if (this.errors.length === 0) {
      this.log('\n🎉 Application appears ready for production!', 'success');
      this.log('Next steps:', 'info');
      this.log('1. Deploy to your hosting platform', 'info');
      this.log('2. Set up environment variables on the platform', 'info');
      this.log('3. Test all functionality after deployment', 'info');
    } else {
      this.log('\n❌ Please fix the errors above before deploying to production', 'error');
    }

    return this.errors.length === 0;
  }

  run() {
    this.log('Starting production setup validation...', 'info');
    
    this.checkEnvironmentVariables();
    this.checkFileStructure();
    this.checkAPIEndpoints();
    this.checkControllers();
    this.checkPackageJson();
    
    return this.generateReport();
  }
}

// Run the setup check
if (require.main === module) {
  const setup = new ProductionSetup();
  const isReady = setup.run();
  process.exit(isReady ? 0 : 1);
}

module.exports = ProductionSetup;
