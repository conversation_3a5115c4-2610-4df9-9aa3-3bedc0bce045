<!-- Edit Project Form -->
<div class="admin-header">
    <h1 class="admin-title">
        <i class="fas fa-edit mr-3"></i>Edit Project: <%= project.title %>
    </h1>
    <p class="text-slate-200 mt-2">Update your project information and documentation</p>
</div>

<div class="form-container">
    <form action="/admin/projects/<%= project._id %>?_method=PUT" method="POST" enctype="multipart/form-data">
        <!-- Basic Information Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-info-circle"></i>Basic Information
            </h3>

            <div class="form-grid">
                <div class="form-group">
                    <label for="title" class="form-label required">Project Title</label>
                    <input type="text" id="title" name="title" class="form-input" value="<%= project.title %>" required>
                    <p class="form-help">Update the project title</p>
                </div>

                <div class="form-group">
                    <label for="authors" class="form-label required">Authors</label>
                    <input type="text" id="authors" name="authors" class="form-input"
                           value="<%= project.authors ? project.authors.join(', ') : '' %>" required>
                    <p class="form-help">List all contributors separated by commas</p>
                </div>
            </div>

            <div class="form-group">
                <label for="description" class="form-label required">Project Description</label>
                <textarea id="description" name="description" rows="6" class="form-textarea" required><%= project.description %></textarea>
                <p class="form-help">Provide a comprehensive description of your project</p>
            </div>

            <div class="form-group">
                <label for="tags" class="form-label">Research Tags</label>
                <input type="text" id="tags" name="tags" class="form-input"
                       value="<%= project.tags ? project.tags.join(', ') : '' %>">
                <p class="form-help">Add relevant tags to help categorize your project</p>
            </div>
        </div>

        <!-- Media & Files Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-images"></i>Media & Documentation
            </h3>

            <div class="form-grid">
                <div class="form-group">
                    <label for="image" class="form-label">Project Image</label>
                    <% if (project.image) { %>
                    <div class="current-file-preview mb-3">
                        <img src="<%= project.image %>" alt="Current image" class="w-24 h-24 object-cover rounded-lg mb-2">
                        <p class="text-sm text-gray-600">Current Image</p>
                    </div>
                    <% } %>
                    <input type="file" id="image" name="image" accept="image/*" class="form-input">
                    <p class="form-help">Upload a new image to replace the current one (JPG, PNG, GIF up to 5MB)</p>
                </div>

                <div class="form-group">
                    <label for="pdfFile" class="form-label">Project Documentation</label>
                    <% if (project.pdfFile) { %>
                    <div class="current-file-preview mb-3">
                        <a href="<%= project.pdfFile %>" target="_blank" class="inline-flex items-center px-3 py-2 bg-red-50 border border-red-200 rounded-lg text-red-700 hover:bg-red-100">
                            <i class="fas fa-file-pdf mr-2"></i>View Current PDF
                        </a>
                    </div>
                    <% } %>
                    <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" class="form-input">
                    <p class="form-help">Upload new documentation to replace the current one (PDF up to 10MB)</p>
                </div>
            </div>
        </div>

        <!-- Project Settings Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-cog"></i>Project Settings
            </h3>

            <div class="checkbox-wrapper">
                <input type="checkbox" id="featured" name="featured" value="true" <%= project.featured ? 'checked' : '' %>>
                <label for="featured" class="font-medium text-gray-700">
                    <span class="text-lg">⭐</span> Mark as Featured Project
                    <p class="text-sm text-gray-500 mt-1">Featured projects appear prominently on the homepage</p>
                </label>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/admin/projects" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Cancel
            </a>
            <div class="flex space-x-3">
                <button type="button" onclick="deleteProject('<%= project._id %>')" class="btn btn-danger">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>Update Project
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Delete Confirmation and Form Validation Script -->
<script>
    function deleteProject(projectId) {
        if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
            fetch(`/admin/projects/${projectId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert('Project deleted successfully');
                    window.location.href = '/admin/projects';
                } else {
                    alert('Error deleting project');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting project');
            });
        }
    }

    document.querySelector('form').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const authors = document.getElementById('authors').value.trim();

        if (!title || !description || !authors) {
            e.preventDefault();
            alert('Please fill in all required fields (Title, Description, Authors)');
        }
    });
</script>