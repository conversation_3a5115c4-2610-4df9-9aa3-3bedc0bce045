# Production API Fix - Blog Posts Not Showing

## Problem Identified

The issue was that in production, the server-side API client was making external HTTP requests to itself instead of internal calls. This caused:

1. **Circular Dependencies**: Server waiting for itself via external network
2. **Timeouts**: External requests taking too long
3. **Connection Failures**: Network issues between server and itself
4. **Performance Issues**: Unnecessary round trips through the internet

## Root Cause

In `utils/apiClient.js`, the base URL was set to:
```javascript
this.baseURL = process.env.BASE_URL || 'http://localhost:3000';
```

In production, `BASE_URL` is set to `https://scienceclub.onrender.com`, causing the server to make HTTP requests to itself via the internet instead of using localhost.

## Solution Applied

### 1. Fixed API Client Configuration

**File**: `utils/apiClient.js`

**Changes**:
- Changed base URL to always use localhost for internal calls
- Added better logging and error handling
- Enhanced debugging capabilities

**Before**:
```javascript
this.baseURL = process.env.BASE_URL || 'http://localhost:3000';
```

**After**:
```javascript
const port = process.env.PORT || 3000;
this.baseURL = `http://localhost:${port}`;
```

### 2. Enhanced Logging

Added comprehensive logging to track API requests and responses:
- Request logging with method and URL
- Response logging with status codes
- Detailed error logging with context

### 3. Added Debug Tools

**New Files**:
- `scripts/test-api-client.js` - Test internal API client
- `scripts/verify-production.js` - Verify production deployment
- `middleware/debugLogger.js` - Debug middleware for troubleshooting

**New API Endpoint**:
- `GET /api/debug` - Check database connectivity and data counts

## Deployment Steps

1. **Push the changes to your repository**
2. **Render will automatically deploy the changes**
3. **Verify the fix using the debug endpoint**

## Verification Commands

### Check API Health
```bash
curl https://scienceclub.onrender.com/api/health
```

### Check Debug Information
```bash
curl https://scienceclub.onrender.com/api/debug
```

### Run Production Verification Script
```bash
node scripts/verify-production.js
```

## Expected Results After Fix

1. **Homepage**: Should show blog posts, projects, and events
2. **Blog Page**: Should display all published blog posts
3. **Projects Page**: Should show all projects
4. **API Responses**: Should be fast and reliable

## Monitoring

After deployment, check the Render logs for:
- `[API Client]` log entries showing localhost requests
- No timeout errors
- Successful API responses

## Troubleshooting

If issues persist:

1. **Check Database Connection**:
   ```bash
   curl https://scienceclub.onrender.com/api/debug
   ```

2. **Verify Data Exists**:
   - Check if `publishedBlogs > 0`
   - Check if `featuredProjects > 0`

3. **Check Logs**:
   - Look for `[API Client]` entries in Render logs
   - Check for any error messages

## Additional Notes

- The frontend API client (`public/js/api-client.js`) remains unchanged as it correctly uses relative URLs
- Admin functionality should continue working normally
- All existing functionality is preserved

## Contact

If you continue experiencing issues after deployment, please check:
1. Render deployment logs
2. The debug endpoint output
3. Browser console for any frontend errors

The fix addresses the core issue of internal API calls and should resolve the problem of posts not showing on the production website.
