const { body, param, query, validationResult } = require('express-validator');
const ApiResponse = require('../utils/apiResponse');

/**
 * Handle validation errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().reduce((acc, error) => {
      acc[error.path] = error.msg;
      return acc;
    }, {});

    return ApiResponse.validationError(res, formattedErrors);
  }

  next();
};

// Common validation rules
const commonValidations = {
  // ID validation
  mongoId: param('id').isMongoId().withMessage('Invalid ID format'),

  // Pagination validation
  page: query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  limit: query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),

  // Search validation
  searchQuery: query('q').optional().isLength({ min: 1, max: 100 }).withMessage('Search query must be between 1 and 100 characters'),

  // Email validation
  email: body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),

  // Password validation
  password: body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),

  // Name validation
  name: body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),

  // Title validation
  title: body('title').trim().isLength({ min: 3, max: 200 }).withMessage('Title must be between 3 and 200 characters'),

  // Description validation
  description: body('description').trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),

  // Message validation
  message: body('message').trim().isLength({ min: 10, max: 1000 }).withMessage('Message must be between 10 and 1000 characters'),

  // Phone validation
  phone: body('phone').optional().isMobilePhone().withMessage('Please provide a valid phone number'),

  // Subject validation
  subject: body('subject').optional().trim().isLength({ max: 200 }).withMessage('Subject cannot exceed 200 characters'),

  // Tags validation
  tags: body('tags').optional().isArray().withMessage('Tags must be an array'),

  // Authors validation
  authors: body('authors').isArray({ min: 1 }).withMessage('At least one author is required'),

  // Date validation
  date: body('date').isISO8601().withMessage('Please provide a valid date'),

  // Boolean validation
  featured: body('featured').optional().isBoolean().withMessage('Featured must be a boolean value'),
  published: body('published').optional().isBoolean().withMessage('Published must be a boolean value'),

  // Category validation
  category: body('category').trim().isLength({ min: 2, max: 50 }).withMessage('Category must be between 2 and 50 characters'),

  // URL validation
  url: body('url').optional().isURL().withMessage('Please provide a valid URL'),

  // Role validation
  role: body('role').optional().isIn(['admin', 'editor']).withMessage('Role must be either admin or editor')
};

// Specific validation chains for different endpoints
const validationChains = {
  // User validations
  createUser: [
    commonValidations.name,
    commonValidations.email,
    commonValidations.password,
    commonValidations.role
  ],

  updateUser: [
    commonValidations.mongoId,
    body('name').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
    body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
    commonValidations.role
  ],

  // Auth validations
  login: [
    commonValidations.email,
    body('password').notEmpty().withMessage('Password is required')
  ],

  // Project validations
  createProject: [
    commonValidations.title,
    commonValidations.description,
    commonValidations.authors,
    commonValidations.tags,
    commonValidations.featured
  ],

  updateProject: [
    commonValidations.mongoId,
    body('title').optional().trim().isLength({ min: 3, max: 200 }).withMessage('Title must be between 3 and 200 characters'),
    body('description').optional().trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),
    body('authors').optional().isArray({ min: 1 }).withMessage('At least one author is required'),
    commonValidations.tags,
    commonValidations.featured
  ],



  // Event validations
  createEvent: [
    commonValidations.title,
    commonValidations.description,
    commonValidations.date,
    body('location').trim().isLength({ min: 3, max: 200 }).withMessage('Location must be between 3 and 200 characters'),
    body('endDate').optional().isISO8601().withMessage('Please provide a valid end date'),
    body('organizer').optional().trim().isLength({ max: 100 }).withMessage('Organizer name cannot exceed 100 characters'),
    body('registrationLink').optional().isURL().withMessage('Please provide a valid registration URL')
  ],

  updateEvent: [
    commonValidations.mongoId,
    body('title').optional().trim().isLength({ min: 3, max: 200 }).withMessage('Title must be between 3 and 200 characters'),
    body('description').optional().trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),
    body('date').optional().isISO8601().withMessage('Please provide a valid date'),
    body('endDate').optional().isISO8601().withMessage('Please provide a valid end date'),
    body('location').optional().trim().isLength({ min: 3, max: 200 }).withMessage('Location must be between 3 and 200 characters'),
    body('organizer').optional().trim().isLength({ max: 100 }).withMessage('Organizer name cannot exceed 100 characters'),
    body('registrationLink').optional().isURL().withMessage('Please provide a valid registration URL')
  ],

  // Resource validations
  createResource: [
    commonValidations.title,
    commonValidations.description,
    commonValidations.category,
    body('type').isIn(['pdf', 'link', 'video']).withMessage('Type must be pdf, link, or video'),
    commonValidations.url,
    body('filePath').optional().isString().withMessage('File path must be a string'),
    commonValidations.tags
  ],

  updateResource: [
    commonValidations.mongoId,
    body('title').optional().trim().isLength({ min: 3, max: 200 }).withMessage('Title must be between 3 and 200 characters'),
    body('description').optional().trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),
    body('category').optional().trim().isLength({ min: 2, max: 50 }).withMessage('Category must be between 2 and 50 characters'),
    body('type').optional().isIn(['pdf', 'link', 'video']).withMessage('Type must be pdf, link, or video'),
    body('url').optional().isURL().withMessage('Please provide a valid URL'),
    body('filePath').optional().isString().withMessage('File path must be a string'),
    commonValidations.tags
  ],

  // Contact validations
  createContact: [
    commonValidations.name,
    commonValidations.email,
    commonValidations.phone,
    commonValidations.subject,
    commonValidations.message
  ],

  // Common validations
  getId: [commonValidations.mongoId],
  getPaginated: [commonValidations.page, commonValidations.limit],
  search: [commonValidations.searchQuery, commonValidations.page, commonValidations.limit]
};

module.exports = {
  handleValidationErrors,
  validationChains,
  commonValidations
};
