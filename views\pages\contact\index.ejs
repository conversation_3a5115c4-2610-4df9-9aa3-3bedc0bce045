<%- contentFor('title') %>
<%= title %>
<%- contentFor('body') %>

<!-- Flash Messages -->
<% if (typeof info !== 'undefined' && info.length > 0) { %>
    <div class="fixed top-4 right-4 z-50 max-w-sm">
        <% info.forEach(function(msg) { %>
            <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg mb-2 shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span><%= msg %></span>
                </div>
            </div>
        <% }); %>
    </div>
<% } %>

<!-- Success Toast -->
<% if (success && successMessage) { %>
    <div id="successToast" class="fixed top-4 right-4 z-50 max-w-sm">
        <div class="bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-xl shadow-xl transform translate-x-full opacity-0 transition-all duration-500 ease-out">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <i class="fas fa-check text-green-600"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <h4 class="font-semibold text-green-800 mb-1">Success!</h4>
                    <p class="text-sm text-green-600 leading-relaxed"><%= successMessage %></p>
                </div>
                <button
                    onclick="closeToast()"
                    class="ml-3 flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full text-green-500 hover:text-green-700 hover:bg-green-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-300"
                    aria-label="Close notification"
                >
                    <i class="fas fa-times text-xs"></i>
                </button>
            </div>
        </div>
    </div>
<% } %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8 sm:py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8 sm:mb-12">
            <div class="flex justify-center mb-6">
                <img src="/images/logo.jpg" alt="Science Club Turbat Logo" class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 object-contain rounded-lg shadow-lg">
            </div>
            <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">Get In Touch</h1>
            <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Have a question, want to collaborate, or interested in joining our community?
                We'd love to hear from you and help you get involved!
            </p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 lg:gap-12">
            <!-- Contact Form -->
            <div class="xl:col-span-2">
                <div class="bg-white rounded-3xl shadow-2xl p-6 sm:p-8 lg:p-10 border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-paper-plane text-blue-600"></i>
                        </div>
                        <h2 class="text-2xl sm:text-3xl font-bold text-gray-900">Send us a Message</h2>
                    </div>

                    <!-- Error Messages -->
                    <% if (Object.keys(errors).length > 0) { %>
                        <div class="bg-red-50 border-l-4 border-red-400 text-red-700 p-4 rounded-lg mb-6">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <h3 class="font-medium">Please fix the following errors:</h3>
                            </div>
                            <% if (errors.general) { %>
                                <p><%= errors.general %></p>
                            <% } else { %>
                                <ul class="list-disc list-inside mt-2 space-y-1">
                                    <% Object.values(errors).forEach(error => { %>
                                        <li><%= error %></li>
                                    <% }); %>
                                </ul>
                            <% } %>
                        </div>
                    <% } %>

                    <form action="/contact" method="POST" class="space-y-6" id="contactForm">
                        <!-- Name and Email Row -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <!-- Name -->
                            <div class="form-group">
                                <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>
                                    Full Name <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    value="<%= formData.name || '' %>"
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 <%= errors.name ? 'border-red-400 focus:border-red-500 focus:ring-red-200' : '' %>"
                                    placeholder="Enter your full name"
                                    required
                                >
                                <% if (errors.name) { %>
                                    <p class="mt-1 text-sm text-red-600"><%= errors.name %></p>
                                <% } %>
                            </div>

                            <!-- Email -->
                            <div class="form-group">
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                    <i class="fas fa-envelope mr-2 text-blue-500"></i>
                                    Email Address <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    value="<%= formData.email || '' %>"
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 <%= errors.email ? 'border-red-400 focus:border-red-500 focus:ring-red-200' : '' %>"
                                    placeholder="Enter your email address"
                                    required
                                >
                                <% if (errors.email) { %>
                                    <p class="mt-1 text-sm text-red-600"><%= errors.email %></p>
                                <% } %>
                            </div>
                        </div>

                        <!-- Phone and Subject Row -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <!-- Phone -->
                            <div class="form-group">
                                <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                    <i class="fas fa-phone mr-2 text-blue-500"></i>
                                    Phone Number
                                </label>
                                <input
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    value="<%= formData.phone || '' %>"
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                    placeholder="Enter your phone number"
                                >
                            </div>

                            <!-- Subject -->
                            <div class="form-group">
                                <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">
                                    <i class="fas fa-tag mr-2 text-blue-500"></i>
                                    Subject
                                </label>
                                <input
                                    type="text"
                                    id="subject"
                                    name="subject"
                                    value="<%= formData.subject || '' %>"
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                    placeholder="What's this about?"
                                >
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="form-group">
                            <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-comment-alt mr-2 text-blue-500"></i>
                                Message <span class="text-red-500">*</span>
                            </label>
                            <textarea
                                id="message"
                                name="message"
                                rows="6"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none <%= errors.message ? 'border-red-400 focus:border-red-500 focus:ring-red-200' : '' %>"
                                placeholder="Tell us about your inquiry, question, or how you'd like to get involved..."
                                required
                            ><%= formData.message || '' %></textarea>
                            <% if (errors.message) { %>
                                <p class="mt-1 text-sm text-red-600"><%= errors.message %></p>
                            <% } %>

                        </div>

                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button
                                type="submit"
                                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 focus:outline-none transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
                                id="submitBtn"
                            >
                                <span class="flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-3"></i>
                                    <span id="btnText">Send Message</span>
                                    <div id="btnSpinner" class="hidden ml-3">
                                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                    </div>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

              <!-- Contact Information -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                <p class="text-lg text-gray-600 mb-8">
                    We'd love to hear from you! Whether you have questions about our research,
                    want to join our community, or are interested in collaboration opportunities,
                    don't hesitate to reach out.
                </p>

                <div class="space-y-6">
                    <!-- Address -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-map-marker-alt text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-1">Address</h3>
                            <p class="text-gray-600">
                                University of Turbat<br>
                                Turbat, Kech<br>
                                Balochistan, Pakistan
                            </p>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-phone text-green-600"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-1">Phone</h3>
                            <p class="text-gray-600">
                                <a href="tel:+923001234567" class="hover:text-blue-600">+92 ************</a>
                            </p>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-envelope text-purple-600"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-1">Email</h3>
                            <p class="text-gray-600">
                                <a href="mailto:<EMAIL>" class="hover:text-blue-600"><EMAIL></a>
                            </p>
                        </div>
                    </div>

                    <!-- Office Hours -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-clock text-yellow-600"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-1">Office Hours</h3>
                            <p class="text-gray-600">
                                Monday - Friday: 9:00 AM - 6:00 PM<br>
                                Saturday: 10:00 AM - 4:00 PM<br>
                                Sunday: Closed
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="mt-8">
                    <h3 class="font-semibold text-gray-900 mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-400 text-white rounded-lg flex items-center justify-center hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-pink-600 text-white rounded-lg flex items-center justify-center hover:bg-pink-700 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-700 text-white rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-red-600 text-white rounded-lg flex items-center justify-center hover:bg-red-700 transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
        </div>
    </div>
</div>

<!-- Contact Form JavaScript -->
<script>
// Function to close toast
function closeToast() {
    const toast = document.getElementById('successToast');
    if (toast) {
        const toastContent = toast.querySelector('div');
        if (toastContent) {
            toastContent.classList.remove('translate-x-0', 'opacity-100');
            toastContent.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 500);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('charCount');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = document.getElementById('btnText');
    const btnSpinner = document.getElementById('btnSpinner');
    const successToast = document.getElementById('successToast');

    // Show success toast with animation
    if (successToast) {
        setTimeout(() => {
            const toastContent = successToast.querySelector('div');
            if (toastContent) {
                toastContent.classList.remove('translate-x-full', 'opacity-0');
                toastContent.classList.add('translate-x-0', 'opacity-100');
            }
        }, 100);

        // Auto-dismiss after 6 seconds (give more time to read)
        setTimeout(() => {
            closeToast();
        }, 6000);

        // Add click outside to close
        successToast.addEventListener('click', function(e) {
            if (e.target === successToast) {
                closeToast();
            }
        });
    }

    // Character counter
    if (messageTextarea && charCount) {
        function updateCharCount() {
            const count = messageTextarea.value.length;
            charCount.textContent = count;

            if (count > 1000) {
                charCount.parentElement.classList.add('text-red-500');
                charCount.parentElement.classList.remove('text-gray-500');
            } else {
                charCount.parentElement.classList.remove('text-red-500');
                charCount.parentElement.classList.add('text-gray-500');
            }
        }

        messageTextarea.addEventListener('input', updateCharCount);
        updateCharCount(); // Initial count
    }

    // Form submission with loading state
    if (form) {
        form.addEventListener('submit', function() {
            submitBtn.disabled = true;
            btnText.textContent = 'Sending...';
            btnSpinner.classList.remove('hidden');
            submitBtn.classList.add('opacity-75');
        });
    }

    // Auto-dismiss flash messages
    const flashMessages = document.querySelectorAll('.fixed.top-4.right-4 > div:not(#successToast)');
    flashMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.opacity = '0';
            message.style.transform = 'translateX(100%)';
            setTimeout(function() {
                message.remove();
            }, 300);
        }, 5000);
    });

    // Add smooth focus animations
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('transform', 'scale-[1.01]');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('transform', 'scale-[1.01]');
        });
    });
});
</script>
