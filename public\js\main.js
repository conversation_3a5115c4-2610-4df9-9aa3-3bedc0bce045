// Science Club Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // Initialize all components
  initMobileMenu();
  initDesktopDropdowns();
  initFormValidation();
  initImagePreview();
  initConfirmDialogs();
  initTooltips();
  initSmoothScrolling();
  initSearchFunctionality();
  initAdminFeatures();

  // Enhanced UI/UX features
  initNavbarScrollEffect();
  initCardAnimations();
  initLoadingAnimations();
  initParallaxEffects();
  initRippleEffects();
  initNewsletterForms();
});

// Modern Mobile menu toggle with animations
function initMobileMenu() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
  const mobileMenuClose = document.getElementById('mobile-menu-close');
  const mobileMenuBackdrop = document.querySelector('.mobile-menu-backdrop');
  const body = document.body;

  if (mobileMenuButton && mobileMenuOverlay) {
    // Enhanced click handling for both hamburger and X states
    mobileMenuButton.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      if (this.classList.contains('active')) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Additional touch event handling for mobile devices
    mobileMenuButton.addEventListener('touchend', function(e) {
      e.preventDefault();
      e.stopPropagation();

      if (this.classList.contains('active')) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Close menu - close button
    if (mobileMenuClose) {
      mobileMenuClose.addEventListener('click', function() {
        closeMobileMenu();
      });
    }

    // Close menu - backdrop click
    if (mobileMenuBackdrop) {
      mobileMenuBackdrop.addEventListener('click', function() {
        closeMobileMenu();
      });
    }

    // Close menu - escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileMenuOverlay.classList.contains('active')) {
        closeMobileMenu();
      }
    });

    // Close menu when clicking nav links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', function() {
        closeMobileMenu();
      });
    });
  }

  function openMobileMenu() {
    mobileMenuOverlay.classList.add('active');
    mobileMenuButton.classList.add('active');
    mobileMenuButton.setAttribute('aria-expanded', 'true');
    body.style.overflow = 'hidden';

    // Add modern entrance animation to the menu panel
    const menuPanel = document.querySelector('.mobile-menu-panel');
    if (menuPanel) {
      menuPanel.style.transform = 'translateX(100%) scale(0.95)';
      setTimeout(() => {
        menuPanel.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        menuPanel.style.transform = 'translateX(0) scale(1)';
      }, 10);
    }

    // Add stagger animation to nav links with modern easing
    const navLinks = document.querySelectorAll('.mobile-nav-link');
    navLinks.forEach((link, index) => {
      link.style.opacity = '0';
      link.style.transform = 'translateX(30px) translateY(10px)';
      setTimeout(() => {
        link.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        link.style.opacity = '1';
        link.style.transform = 'translateX(0) translateY(0)';
      }, 150 + (index * 60));
    });

    // Animate search section
    const searchSection = document.querySelector('.mobile-menu-content > div:first-child');
    if (searchSection) {
      searchSection.style.opacity = '0';
      searchSection.style.transform = 'translateY(-20px)';
      setTimeout(() => {
        searchSection.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        searchSection.style.opacity = '1';
        searchSection.style.transform = 'translateY(0)';
      }, 100);
    }
  }

  function closeMobileMenu() {
    mobileMenuOverlay.classList.remove('active');
    mobileMenuButton.classList.remove('active');
    mobileMenuButton.setAttribute('aria-expanded', 'false');
    body.style.overflow = '';

    // Reset all animations
    const menuPanel = document.querySelector('.mobile-menu-panel');
    const navLinks = document.querySelectorAll('.mobile-nav-link');
    const searchSection = document.querySelector('.mobile-menu-content > div:first-child');

    // Reset menu panel
    if (menuPanel) {
      menuPanel.style.transition = '';
      menuPanel.style.transform = '';
    }

    // Reset nav links
    navLinks.forEach(link => {
      link.style.transition = '';
      link.style.opacity = '';
      link.style.transform = '';
    });

    // Reset search section
    if (searchSection) {
      searchSection.style.transition = '';
      searchSection.style.opacity = '';
      searchSection.style.transform = '';
    }
  }
}

// Enhanced Desktop Navigation with Dropdowns
function initDesktopDropdowns() {
  const dropdowns = document.querySelectorAll('.nav-dropdown');
  let activeDropdown = null;
  let hoverTimeout = null;

  dropdowns.forEach(dropdown => {
    const trigger = dropdown.querySelector('.dropdown-trigger');
    const menu = dropdown.querySelector('.dropdown-menu');

    if (trigger && menu) {
      // Mouse enter - show dropdown
      dropdown.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);

        // Close other dropdowns
        if (activeDropdown && activeDropdown !== dropdown) {
          hideDropdown(activeDropdown);
        }

        showDropdown(dropdown);
        activeDropdown = dropdown;
      });

      // Mouse leave - hide dropdown with delay
      dropdown.addEventListener('mouseleave', function() {
        hoverTimeout = setTimeout(() => {
          hideDropdown(dropdown);
          if (activeDropdown === dropdown) {
            activeDropdown = null;
          }
        }, 150); // Small delay to prevent flickering
      });

      // Keyboard navigation
      trigger.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (activeDropdown === dropdown) {
            hideDropdown(dropdown);
            activeDropdown = null;
          } else {
            if (activeDropdown) {
              hideDropdown(activeDropdown);
            }
            showDropdown(dropdown);
            activeDropdown = dropdown;
          }
        } else if (e.key === 'Escape') {
          hideDropdown(dropdown);
          activeDropdown = null;
          trigger.focus();
        }
      });

      // Handle dropdown item navigation
      const dropdownItems = menu.querySelectorAll('.dropdown-item');
      dropdownItems.forEach((item, index) => {
        item.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowDown') {
            e.preventDefault();
            const nextItem = dropdownItems[index + 1];
            if (nextItem) {
              nextItem.focus();
            }
          } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            const prevItem = dropdownItems[index - 1];
            if (prevItem) {
              prevItem.focus();
            } else {
              trigger.focus();
            }
          } else if (e.key === 'Escape') {
            hideDropdown(dropdown);
            activeDropdown = null;
            trigger.focus();
          }
        });
      });
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', function(e) {
    if (activeDropdown && !activeDropdown.contains(e.target)) {
      hideDropdown(activeDropdown);
      activeDropdown = null;
    }
  });

  // Close dropdowns on scroll
  window.addEventListener('scroll', function() {
    if (activeDropdown) {
      hideDropdown(activeDropdown);
      activeDropdown = null;
    }
  });

  function showDropdown(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const trigger = dropdown.querySelector('.dropdown-trigger');

    if (menu && trigger) {
      menu.style.opacity = '1';
      menu.style.visibility = 'visible';
      menu.style.transform = 'translateX(-50%) translateY(0)';

      trigger.setAttribute('aria-expanded', 'true');

      // Add active class for styling
      dropdown.classList.add('active');

      // Focus first dropdown item for keyboard navigation
      const firstItem = menu.querySelector('.dropdown-item');
      if (firstItem && document.activeElement === trigger) {
        setTimeout(() => firstItem.focus(), 100);
      }
    }
  }

  function hideDropdown(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const trigger = dropdown.querySelector('.dropdown-trigger');

    if (menu && trigger) {
      menu.style.opacity = '0';
      menu.style.visibility = 'hidden';
      menu.style.transform = 'translateX(-50%) translateY(-10px)';

      trigger.setAttribute('aria-expanded', 'false');

      // Remove active class
      dropdown.classList.remove('active');
    }
  }
}

// Form validation
function initFormValidation() {
  const forms = document.querySelectorAll('form[data-validate]');

  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      if (!validateForm(form)) {
        e.preventDefault();
      }
    });
  });
}

function validateForm(form) {
  let isValid = true;
  const requiredFields = form.querySelectorAll('[required]');

  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      showFieldError(field, 'This field is required');
      isValid = false;
    } else {
      clearFieldError(field);
    }
  });

  // Email validation
  const emailFields = form.querySelectorAll('input[type="email"]');
  emailFields.forEach(field => {
    if (field.value && !isValidEmail(field.value)) {
      showFieldError(field, 'Please enter a valid email address');
      isValid = false;
    }
  });

  return isValid;
}

function showFieldError(field, message) {
  clearFieldError(field);

  field.classList.add('border-red-500');
  const errorDiv = document.createElement('div');
  errorDiv.className = 'text-red-500 text-sm mt-1';
  errorDiv.textContent = message;
  errorDiv.setAttribute('data-error', 'true');

  field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
  field.classList.remove('border-red-500');
  const errorDiv = field.parentNode.querySelector('[data-error]');
  if (errorDiv) {
    errorDiv.remove();
  }
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Image preview for file uploads
function initImagePreview() {
  const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');

  imageInputs.forEach(input => {
    input.addEventListener('change', function(e) {
      const file = e.target.files[0];
      const previewId = input.getAttribute('data-preview');
      const preview = document.getElementById(previewId);

      if (file && preview) {
        const reader = new FileReader();
        reader.onload = function(e) {
          preview.src = e.target.result;
          preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
      }
    });
  });
}

// Confirm dialogs for delete actions
function initConfirmDialogs() {
  const deleteButtons = document.querySelectorAll('[data-confirm]');

  deleteButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      const message = button.getAttribute('data-confirm');
      if (!confirm(message)) {
        e.preventDefault();
      }
    });
  });
}

// Tooltips
function initTooltips() {
  const tooltipElements = document.querySelectorAll('[data-tooltip]');

  tooltipElements.forEach(element => {
    element.addEventListener('mouseenter', showTooltip);
    element.addEventListener('mouseleave', hideTooltip);
  });
}

function showTooltip(e) {
  const element = e.target;
  const tooltipText = element.getAttribute('data-tooltip');

  const tooltip = document.createElement('div');
  tooltip.className = 'absolute bg-gray-900 text-white text-sm px-2 py-1 rounded shadow-lg z-50';
  tooltip.textContent = tooltipText;
  tooltip.id = 'tooltip';

  document.body.appendChild(tooltip);

  const rect = element.getBoundingClientRect();
  tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
  tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

function hideTooltip() {
  const tooltip = document.getElementById('tooltip');
  if (tooltip) {
    tooltip.remove();
  }
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
  const anchorLinks = document.querySelectorAll('a[href^="#"]');

  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });
}

// Enhanced Search functionality
function initSearchFunctionality() {
  const searchInput = document.getElementById('search-input');
  const mobileSearchInput = document.getElementById('mobile-search-input');
  const searchResults = document.getElementById('search-results');

  // Desktop search
  if (searchInput) {
    initSearchInput(searchInput, searchResults);
  }

  // Mobile search
  if (mobileSearchInput) {
    initSearchInput(mobileSearchInput, null, true);
    initMobileSearchHints();
  }
}

// Initialize mobile search hints functionality
function initMobileSearchHints() {
  const searchHints = document.querySelectorAll('.mobile-menu-content .flex-wrap span');
  const mobileSearchInput = document.getElementById('mobile-search-input');

  if (searchHints && mobileSearchInput) {
    searchHints.forEach(hint => {
      hint.addEventListener('click', function() {
        const searchTerm = this.textContent.trim();
        mobileSearchInput.value = searchTerm;
        mobileSearchInput.focus();

        // Trigger search
        window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;
      });
    });
  }
}

function initSearchInput(input, resultsContainer, isMobile = false) {
  let searchTimeout;
  let currentRequest = null;

  input.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();

    if (query.length >= 2) {
      searchTimeout = setTimeout(() => {
        performSearch(query, resultsContainer, isMobile);
      }, 300);
    } else {
      if (resultsContainer) {
        resultsContainer.innerHTML = '';
        resultsContainer.classList.add('hidden');
      }
    }
  });

  // Handle Enter key for mobile search
  if (isMobile) {
    input.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const query = this.value.trim();
        if (query.length >= 2) {
          window.location.href = `/search?q=${encodeURIComponent(query)}`;
        }
      }
    });
  }

  // Handle clicks outside to close results
  document.addEventListener('click', function(e) {
    if (resultsContainer && !input.contains(e.target) && !resultsContainer.contains(e.target)) {
      resultsContainer.classList.add('hidden');
    }
  });
}

async function performSearch(query, resultsContainer, isMobile = false) {
  if (isMobile) {
    // For mobile, just redirect to search page
    return;
  }

  if (!resultsContainer) return;

  try {
    // Show loading state
    resultsContainer.innerHTML = `
      <div class="p-4 flex items-center">
        <div class="spinner mr-3"></div>
        <span class="text-gray-600">Searching for "${query}"...</span>
      </div>
    `;
    resultsContainer.classList.remove('hidden');

    // Make API request
    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&limit=5`);
    const data = await response.json();

    if (data.success && data.data && data.data.length > 0) {
      // API returns data in format: { success: true, data: [...], pagination: {...} }
      displaySearchResults(data.data, data.pagination?.totalItems || data.data.length, query, resultsContainer);
    } else {
      displayNoResults(query, resultsContainer);
    }

  } catch (error) {
    console.error('Search error:', error);
    resultsContainer.innerHTML = `
      <div class="p-4 text-red-600">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        Search failed. Please try again.
      </div>
    `;
  }
}

function displaySearchResults(results, total, query, container) {
  const resultsHtml = results.map(result => `
    <a href="${result.url}" class="block p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors">
      <div class="flex items-start">
        <div class="flex-shrink-0 mr-3 mt-1">
          ${getResultIcon(result.type)}
        </div>
        <div class="flex-1 min-w-0">
          <h4 class="text-sm font-medium text-gray-900 truncate">${result.title}</h4>
          <p class="text-sm text-gray-600 mt-1 line-clamp-2">${result.description}</p>
          <div class="flex items-center mt-2 text-xs text-gray-500">
            <span class="capitalize">${result.type}</span>
            <span class="mx-2">•</span>
            <span>${new Date(result.createdAt || result.date).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </a>
  `).join('');

  const footerHtml = total > results.length ? `
    <div class="p-4 bg-gray-50 border-t border-gray-200">
      <a href="/search?q=${encodeURIComponent(query)}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
        View all ${total} results →
      </a>
    </div>
  ` : '';

  container.innerHTML = resultsHtml + footerHtml;
  container.classList.remove('hidden');
}

function displayNoResults(query, container) {
  container.innerHTML = `
    <div class="p-4 text-center">
      <i class="fas fa-search text-gray-300 text-2xl mb-2"></i>
      <p class="text-gray-600 text-sm">No results found for "${query}"</p>
      <a href="/search?q=${encodeURIComponent(query)}" class="text-blue-600 hover:text-blue-800 text-sm">
        Try advanced search →
      </a>
    </div>
  `;
  container.classList.remove('hidden');
}

function getResultIcon(type) {
  const icons = {
    project: '<i class="fas fa-flask text-blue-600"></i>',
    blog: '<i class="fas fa-newspaper text-green-600"></i>',
    resource: '<i class="fas fa-book text-purple-600"></i>',
    event: '<i class="fas fa-calendar text-orange-600"></i>'
  };
  return icons[type] || '<i class="fas fa-file text-gray-600"></i>';
}



// Admin-specific features
function initAdminFeatures() {
  initAjaxForms();
  initDataTables();
  initStatusToggles();
  initBulkActions();
}

// AJAX forms for admin actions
function initAjaxForms() {
  const ajaxForms = document.querySelectorAll('form[data-ajax]');

  ajaxForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(form);
      const url = form.action;
      const method = form.method || 'POST';

      fetch(url, {
        method: method,
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('Success!', 'success');
          if (data.redirect) {
            window.location.href = data.redirect;
          }
        } else {
          showNotification(data.error || 'An error occurred', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
      });
    });
  });
}

// Data tables with sorting and filtering
function initDataTables() {
  const tables = document.querySelectorAll('table[data-sortable]');

  tables.forEach(table => {
    const headers = table.querySelectorAll('th[data-sort]');

    headers.forEach(header => {
      header.addEventListener('click', function() {
        const column = this.getAttribute('data-sort');
        sortTable(table, column);
      });
    });
  });
}

function sortTable(table, column) {
  // Basic table sorting implementation
  const tbody = table.querySelector('tbody');
  const rows = Array.from(tbody.querySelectorAll('tr'));

  rows.sort((a, b) => {
    const aValue = a.querySelector(`td[data-${column}]`).textContent.trim();
    const bValue = b.querySelector(`td[data-${column}]`).textContent.trim();

    return aValue.localeCompare(bValue);
  });

  rows.forEach(row => tbody.appendChild(row));
}

// Status toggles
function initStatusToggles() {
  const toggles = document.querySelectorAll('[data-toggle-status]');

  toggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const url = this.getAttribute('data-url');

      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          this.textContent = data.newStatus;
          showNotification('Status updated', 'success');
        } else {
          showNotification('Error updating status', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating status', 'error');
      });
    });
  });
}

// Bulk actions
function initBulkActions() {
  const selectAllCheckbox = document.getElementById('select-all');
  const itemCheckboxes = document.querySelectorAll('input[name="selected_items[]"]');
  const bulkActionButton = document.getElementById('bulk-action-button');

  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
      itemCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateBulkActionButton();
    });
  }

  itemCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButton);
  });

  function updateBulkActionButton() {
    const selectedCount = document.querySelectorAll('input[name="selected_items[]"]:checked').length;
    if (bulkActionButton) {
      bulkActionButton.style.display = selectedCount > 0 ? 'block' : 'none';
      bulkActionButton.textContent = `Actions (${selectedCount} selected)`;
    }
  }
}

// Notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${getNotificationClasses(type)}`;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.remove();
  }, 5000);

  // Remove on click
  notification.addEventListener('click', () => {
    notification.remove();
  });
}

function getNotificationClasses(type) {
  switch (type) {
    case 'success':
      return 'bg-green-500 text-white';
    case 'error':
      return 'bg-red-500 text-white';
    case 'warning':
      return 'bg-yellow-500 text-white';
    default:
      return 'bg-blue-500 text-white';
  }
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Enhanced UI/UX Features

// Navbar scroll effect
function initNavbarScrollEffect() {
  const navbar = document.querySelector('.navbar');
  if (!navbar) return;

  let lastScrollY = window.scrollY;
  let ticking = false;

  function updateNavbar() {
    const scrollY = window.scrollY;

    if (scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }

    // Hide/show navbar on scroll
    if (scrollY > lastScrollY && scrollY > 100) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }

    lastScrollY = scrollY;
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateNavbar);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick);
}

// Enhanced card animations
function initCardAnimations() {
  const cards = document.querySelectorAll('.card, .project-card, .blog-card, .event-card');

  // Intersection Observer for scroll animations
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });

  cards.forEach((card, index) => {
    // Initial state
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;

    observer.observe(card);
  });
}

// Loading animations for dynamic content
function initLoadingAnimations() {
  const buttons = document.querySelectorAll('.btn');

  buttons.forEach(button => {
    button.addEventListener('click', function(e) {
      // Add loading state for form submissions
      if (this.type === 'submit' || this.closest('form')) {
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
        this.disabled = true;

        // Reset after 3 seconds (adjust based on your needs)
        setTimeout(() => {
          this.innerHTML = originalText;
          this.disabled = false;
        }, 3000);
      }
    });
  });
}

// Parallax effects for hero section
function initParallaxEffects() {
  const hero = document.querySelector('.hero');
  if (!hero) return;

  let ticking = false;

  function updateParallax() {
    const scrolled = window.pageYOffset;
    const parallax = hero.querySelector('.hero-content');

    if (parallax && scrolled < window.innerHeight) {
      const speed = scrolled * 0.3;
      parallax.style.transform = `translateY(${speed}px)`;
    }

    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateParallax);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick);
}

// Ripple effects for buttons
function initRippleEffects() {
  // Add CSS for ripple effect
  const rippleCSS = `
    .btn {
      position: relative;
      overflow: hidden;
    }

    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0);
      animation: ripple-animation 0.6s linear;
      pointer-events: none;
    }

    @keyframes ripple-animation {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  `;

  // Inject ripple CSS
  const style = document.createElement('style');
  style.textContent = rippleCSS;
  document.head.appendChild(style);

  // Add ripple effect to buttons
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('btn') || e.target.closest('.btn')) {
      const button = e.target.classList.contains('btn') ? e.target : e.target.closest('.btn');
      addRippleEffect(button, e);
    }
  });
}

function addRippleEffect(button, e) {
  const ripple = document.createElement('span');
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = e.clientX - rect.left - size / 2;
  const y = e.clientY - rect.top - size / 2;

  ripple.style.width = ripple.style.height = size + 'px';
  ripple.style.left = x + 'px';
  ripple.style.top = y + 'px';
  ripple.classList.add('ripple');

  button.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

// Enhanced Newsletter Forms
function initNewsletterForms() {
  const newsletterForms = document.querySelectorAll('form[data-newsletter]');

  newsletterForms.forEach(form => {
    const emailInput = form.querySelector('input[type="email"]');
    const submitButton = form.querySelector('button[type="submit"]');

    // Enhanced email validation with suggestions
    emailInput.addEventListener('blur', function() {
      validateEmailWithSuggestions(this);
    });

    // Real-time validation
    emailInput.addEventListener('input', function() {
      clearEmailError(this);
      if (this.value.length > 0) {
        validateEmailFormat(this);
      }
    });

    // Enhanced form submission
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      handleNewsletterSubmission(this);
    });

    // Add floating label effect
    addFloatingLabelEffect(emailInput);
  });
}

function validateEmailWithSuggestions(input) {
  const email = input.value.trim();
  if (!email) return;

  // Common domain suggestions
  const commonDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'university.edu', 'research.edu', 'mit.edu', 'stanford.edu',
    'harvard.edu', 'berkeley.edu', 'caltech.edu'
  ];

  const emailParts = email.split('@');
  if (emailParts.length === 2) {
    const domain = emailParts[1].toLowerCase();

    // Check for common typos
    const suggestions = commonDomains.filter(d =>
      d.includes(domain) || domain.includes(d.split('.')[0])
    );

    if (suggestions.length > 0 && !commonDomains.includes(domain)) {
      showEmailSuggestion(input, emailParts[0] + '@' + suggestions[0]);
    }
  }
}

function showEmailSuggestion(input, suggestion) {
  clearEmailError(input);

  const suggestionDiv = document.createElement('div');
  suggestionDiv.className = 'email-suggestion mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700';
  suggestionDiv.innerHTML = `
    <i class="fas fa-lightbulb mr-1"></i>
    Did you mean <button type="button" class="font-semibold underline hover:no-underline" onclick="acceptEmailSuggestion('${input.id}', '${suggestion}')">${suggestion}</button>?
  `;

  input.parentNode.appendChild(suggestionDiv);
}

function acceptEmailSuggestion(inputId, suggestion) {
  const input = document.getElementById(inputId) || document.querySelector(`input[type="email"]`);
  input.value = suggestion;
  clearEmailError(input);
  input.focus();
}

function validateEmailFormat(input) {
  const email = input.value.trim();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (email && !emailRegex.test(email)) {
    showEmailError(input, 'Please enter a valid email address');
    return false;
  }
  return true;
}

function showEmailError(input, message) {
  clearEmailError(input);

  input.classList.add('border-red-500', 'focus:border-red-500');
  const errorDiv = document.createElement('div');
  errorDiv.className = 'email-error mt-1 text-red-500 text-sm';
  errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i>${message}`;

  input.parentNode.appendChild(errorDiv);
}

function clearEmailError(input) {
  input.classList.remove('border-red-500', 'focus:border-red-500');
  const errorDiv = input.parentNode.querySelector('.email-error');
  const suggestionDiv = input.parentNode.querySelector('.email-suggestion');

  if (errorDiv) errorDiv.remove();
  if (suggestionDiv) suggestionDiv.remove();
}

function handleNewsletterSubmission(form) {
  const emailInput = form.querySelector('input[type="email"]');
  const submitButton = form.querySelector('button[type="submit"]');
  const email = emailInput.value.trim();

  // Validate email
  if (!validateEmailFormat(emailInput)) {
    return;
  }

  // Show loading state
  const originalButtonText = submitButton.innerHTML;
  submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Subscribing...';
  submitButton.disabled = true;

  // Simulate API call (replace with actual endpoint)
  setTimeout(() => {
    // Success state
    showNewsletterSuccess(form, email);

    // Reset button after delay
    setTimeout(() => {
      submitButton.innerHTML = originalButtonText;
      submitButton.disabled = false;
    }, 3000);
  }, 1500);
}

function showNewsletterSuccess(form, email) {
  const formContainer = form.parentNode;

  // Create success message
  const successDiv = document.createElement('div');
  successDiv.className = 'newsletter-success text-center p-6 bg-green-50 border border-green-200 rounded-lg';
  successDiv.innerHTML = `
    <div class="mb-4">
      <i class="fas fa-check-circle text-green-500 text-3xl"></i>
    </div>
    <h3 class="text-lg font-semibold text-green-800 mb-2">Welcome to our community!</h3>
    <p class="text-green-700 mb-4">
      We've sent a confirmation email to <strong>${email}</strong>
    </p>
    <p class="text-sm text-green-600">
      <i class="fas fa-envelope mr-1"></i>
      Check your inbox and click the confirmation link to complete your subscription.
    </p>
  `;

  // Replace form with success message
  form.style.opacity = '0';
  form.style.transform = 'translateY(-20px)';

  setTimeout(() => {
    form.style.display = 'none';
    formContainer.appendChild(successDiv);

    // Animate in success message
    successDiv.style.opacity = '0';
    successDiv.style.transform = 'translateY(20px)';

    setTimeout(() => {
      successDiv.style.transition = 'all 0.5s ease';
      successDiv.style.opacity = '1';
      successDiv.style.transform = 'translateY(0)';
    }, 100);
  }, 300);
}

function addFloatingLabelEffect(input) {
  const placeholder = input.getAttribute('placeholder');

  input.addEventListener('focus', function() {
    this.setAttribute('data-placeholder', placeholder);
    this.setAttribute('placeholder', '');
  });

  input.addEventListener('blur', function() {
    if (!this.value) {
      this.setAttribute('placeholder', this.getAttribute('data-placeholder') || placeholder);
    }
  });
}
