const express = require('express');
const router = express.Router();
const User = require('../../models/User');
const ApiResponse = require('../../utils/apiResponse');
const { verifyToken, requireAdmin } = require('../../middleware/apiAuth');
const { adminLimiter } = require('../../middleware/rateLimiter');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

// Apply admin rate limiting to all user management routes
router.use(adminLimiter);

/**
 * @route   GET /api/users
 * @desc    Get all users with pagination
 * @access  Private (Admin only)
 */
router.get('/',
  verifyToken,
  requireAdmin,
  validationChains.getPaginated,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query
      const query = {};

      // Filter by role if specified
      if (req.query.role) {
        query.role = req.query.role;
      }

      // Search by name or email
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { email: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const users = await User.find(query)
        .select('-password') // Exclude password field
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await User.countDocuments(query);
      const totalPages = Math.ceil(total / limit);

      const pagination = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      ApiResponse.paginated(res, users, pagination, 'Users retrieved successfully');

    } catch (error) {
      console.error('Get users error:', error);
      ApiResponse.error(res, 'Failed to retrieve users');
    }
  }
);

/**
 * @route   GET /api/users/stats/summary
 * @desc    Get user statistics
 * @access  Private (Admin only)
 */
router.get('/stats/summary',
  verifyToken,
  requireAdmin,
  async (req, res) => {
    try {
      const totalUsers = await User.countDocuments();
      const adminUsers = await User.countDocuments({ role: 'admin' });
      const editorUsers = await User.countDocuments({ role: 'editor' });

      // Get users created in last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentUsers = await User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
      });

      const stats = {
        total: totalUsers,
        admins: adminUsers,
        editors: editorUsers,
        recent: recentUsers
      };

      ApiResponse.success(res, stats, 'User statistics retrieved successfully');

    } catch (error) {
      console.error('Get user stats error:', error);
      ApiResponse.error(res, 'Failed to retrieve user statistics');
    }
  }
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private (Admin only)
 */
router.get('/:id',
  verifyToken,
  requireAdmin,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id).select('-password');

      if (!user) {
        return ApiResponse.notFound(res, 'User not found');
      }

      ApiResponse.success(res, user, 'User retrieved successfully');

    } catch (error) {
      console.error('Get user error:', error);
      ApiResponse.error(res, 'Failed to retrieve user');
    }
  }
);

/**
 * @route   POST /api/users
 * @desc    Create new user
 * @access  Private (Admin only)
 */
router.post('/',
  verifyToken,
  requireAdmin,
  validationChains.createUser,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { name, email, password, role } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({ email: email.toLowerCase() });
      if (existingUser) {
        return ApiResponse.error(res, 'User with this email already exists', 400);
      }

      const userData = {
        name,
        email: email.toLowerCase(),
        password,
        role: role || 'admin'
      };

      const user = new User(userData);
      await user.save();

      // Return user data without password
      const userResponse = {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt
      };

      ApiResponse.success(res, userResponse, 'User created successfully', 201);

    } catch (error) {
      console.error('Create user error:', error);
      ApiResponse.error(res, 'Failed to create user');
    }
  }
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private (Admin only)
 */
router.put('/:id',
  verifyToken,
  requireAdmin,
  validationChains.updateUser,
  handleValidationErrors,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return ApiResponse.notFound(res, 'User not found');
      }

      // Check if email is being changed and if it already exists
      if (req.body.email && req.body.email.toLowerCase() !== user.email) {
        const existingUser = await User.findOne({ email: req.body.email.toLowerCase() });
        if (existingUser) {
          return ApiResponse.error(res, 'User with this email already exists', 400);
        }
      }

      // Update fields (password update requires special handling)
      const allowedUpdates = ['name', 'email', 'role'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          if (field === 'email') {
            user[field] = req.body[field].toLowerCase();
          } else {
            user[field] = req.body[field];
          }
        }
      });

      // Handle password update separately
      if (req.body.password) {
        user.password = req.body.password; // Will be hashed by pre-save middleware
      }

      await user.save();

      // Return user data without password
      const userResponse = {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt
      };

      ApiResponse.success(res, userResponse, 'User updated successfully');

    } catch (error) {
      console.error('Update user error:', error);
      ApiResponse.error(res, 'Failed to update user');
    }
  }
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user
 * @access  Private (Admin only)
 */
router.delete('/:id',
  verifyToken,
  requireAdmin,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return ApiResponse.notFound(res, 'User not found');
      }

      // Prevent admin from deleting themselves
      if (user._id.toString() === req.user._id.toString()) {
        return ApiResponse.error(res, 'You cannot delete your own account', 400);
      }

      await User.findByIdAndDelete(req.params.id);

      ApiResponse.success(res, null, 'User deleted successfully');

    } catch (error) {
      console.error('Delete user error:', error);
      ApiResponse.error(res, 'Failed to delete user');
    }
  }
);

module.exports = router;
