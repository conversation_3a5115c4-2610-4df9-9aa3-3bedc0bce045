const express = require('express');
const router = express.Router();
const Event = require('../../models/Event');
const ApiResponse = require('../../utils/apiResponse');
const { verifyToken, requireEditor } = require('../../middleware/apiAuth');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

/**
 * @route   GET /api/events
 * @desc    Get all events with pagination
 * @access  Public
 */
router.get('/',
  validationChains.getPaginated,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query
      const query = {};

      // Filter by upcoming/past events
      if (req.query.upcoming === 'true') {
        query.date = { $gte: new Date() };
        query.isPast = false;
      } else if (req.query.past === 'true') {
        query.isPast = true;
      }

      // Search by title, description, or location
      if (req.query.search) {
        query.$or = [
          { title: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } },
          { location: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const events = await Event.find(query)
        .sort({ date: req.query.upcoming === 'true' ? 1 : -1 })
        .skip(skip)
        .limit(limit);

      const total = await Event.countDocuments(query);
      const totalPages = Math.ceil(total / limit);

      const pagination = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      ApiResponse.paginated(res, events, pagination, 'Events retrieved successfully');

    } catch (error) {
      console.error('Get events error:', error);
      ApiResponse.error(res, 'Failed to retrieve events');
    }
  }
);

/**
 * @route   GET /api/events/upcoming
 * @desc    Get upcoming events
 * @access  Public
 */
router.get('/upcoming', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;

    const events = await Event.find({
      date: { $gte: new Date() },
      isPast: false
    })
      .sort({ date: 1 })
      .limit(limit);

    ApiResponse.success(res, events, 'Upcoming events retrieved successfully');

  } catch (error) {
    console.error('Get upcoming events error:', error);
    ApiResponse.error(res, 'Failed to retrieve upcoming events');
  }
});

/**
 * @route   GET /api/events/:id
 * @desc    Get event by ID
 * @access  Public
 */
router.get('/:id',
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return ApiResponse.notFound(res, 'Event not found');
      }

      ApiResponse.success(res, event, 'Event retrieved successfully');

    } catch (error) {
      console.error('Get event error:', error);
      ApiResponse.error(res, 'Failed to retrieve event');
    }
  }
);

/**
 * @route   POST /api/events
 * @desc    Create new event
 * @access  Private (Editor/Admin)
 */
router.post('/',
  verifyToken,
  requireEditor,
  validationChains.createEvent,
  handleValidationErrors,
  async (req, res) => {
    try {
      const eventData = {
        title: req.body.title,
        description: req.body.description,
        date: new Date(req.body.date),
        location: req.body.location,
        organizer: req.body.organizer || 'Science Club'
      };

      // Handle optional fields
      if (req.body.endDate) {
        eventData.endDate = new Date(req.body.endDate);
      }
      if (req.body.registrationLink) {
        eventData.registrationLink = req.body.registrationLink;
      }
      if (req.body.image) {
        eventData.image = req.body.image;
      }
      if (req.body.featured !== undefined) {
        eventData.featured = req.body.featured;
      }

      // Set isPast based on date
      eventData.isPast = eventData.date < new Date();

      const event = new Event(eventData);
      await event.save();

      ApiResponse.success(res, event, 'Event created successfully', 201);

    } catch (error) {
      console.error('Create event error:', error);
      ApiResponse.error(res, 'Failed to create event');
    }
  }
);

/**
 * @route   PUT /api/events/:id
 * @desc    Update event
 * @access  Private (Editor/Admin)
 */
router.put('/:id',
  verifyToken,
  requireEditor,
  validationChains.updateEvent,
  handleValidationErrors,
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return ApiResponse.notFound(res, 'Event not found');
      }

      // Update fields
      const allowedUpdates = ['title', 'description', 'date', 'endDate', 'location', 'organizer', 'registrationLink', 'image', 'featured'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          if (field === 'date' || field === 'endDate') {
            event[field] = new Date(req.body[field]);
          } else {
            event[field] = req.body[field];
          }
        }
      });

      // Update isPast based on date
      event.isPast = event.date < new Date();

      await event.save();

      ApiResponse.success(res, event, 'Event updated successfully');

    } catch (error) {
      console.error('Update event error:', error);
      ApiResponse.error(res, 'Failed to update event');
    }
  }
);

/**
 * @route   DELETE /api/events/:id
 * @desc    Delete event
 * @access  Private (Editor/Admin)
 */
router.delete('/:id',
  verifyToken,
  requireEditor,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return ApiResponse.notFound(res, 'Event not found');
      }

      await Event.findByIdAndDelete(req.params.id);

      ApiResponse.success(res, null, 'Event deleted successfully');

    } catch (error) {
      console.error('Delete event error:', error);
      ApiResponse.error(res, 'Failed to delete event');
    }
  }
);

module.exports = router;
