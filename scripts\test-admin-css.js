#!/usr/bin/env node

/**
 * Test Admin CSS Loading
 * Simple test to check if admin dashboard CSS is loading correctly
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function testAdminCSS() {
  const baseURL = process.env.BASE_URL || 'http://localhost:10000';
  
  console.log('Testing Admin CSS Loading...');
  console.log(`Base URL: ${baseURL}`);
  
  try {
    // Test if Tailwind CDN is accessible
    console.log('\n1. Testing Tailwind CDN accessibility...');
    try {
      const tailwindResponse = await axios.get('https://cdn.tailwindcss.com', { timeout: 5000 });
      console.log(`✓ Tailwind CDN accessible: ${tailwindResponse.status}`);
    } catch (tailwindError) {
      console.log(`✗ Tailwind CDN not accessible: ${tailwindError.message}`);
    }
    
    // Test admin login page (should load without authentication)
    console.log('\n2. Testing admin login page...');
    const loginResponse = await axios.get(`${baseURL}/admin/login`, { timeout: 10000 });
    console.log(`✓ Admin login page: ${loginResponse.status}`);
    
    // Check if response contains Tailwind CSS script tag
    if (loginResponse.data.includes('cdn.tailwindcss.com')) {
      console.log('✓ Tailwind CSS script tag found in response');
    } else {
      console.log('✗ Tailwind CSS script tag NOT found in response');
    }
    
    // Check if response contains Font Awesome
    if (loginResponse.data.includes('font-awesome')) {
      console.log('✓ Font Awesome found in response');
    } else {
      console.log('✗ Font Awesome NOT found in response');
    }
    
    // Test static CSS file
    console.log('\n3. Testing static CSS file...');
    try {
      const cssResponse = await axios.get(`${baseURL}/css/style.css`, { timeout: 5000 });
      console.log(`✓ Static CSS file accessible: ${cssResponse.status}`);
    } catch (cssError) {
      console.log(`✗ Static CSS file not accessible: ${cssError.message}`);
    }
    
    // Test if server is running on correct port
    console.log('\n4. Testing server configuration...');
    console.log(`Server should be running on port: ${process.env.PORT || 10000}`);
    
    if (baseURL.includes('10000')) {
      console.log('✓ Using correct port 10000');
    } else {
      console.log('⚠ Not using port 10000 - this might cause issues');
    }
    
    console.log('\n🎉 CSS loading test completed!');
    console.log('\nNext steps to fix admin dashboard styling:');
    console.log('1. Make sure the server is running on port 10000');
    console.log('2. Check browser console for any JavaScript errors');
    console.log('3. Verify Tailwind CDN is not blocked by firewall/antivirus');
    console.log('4. Clear browser cache and reload the page');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('Server is not running. Please start the server with: npm start');
    }
    process.exit(1);
  }
}

// Run the test
testAdminCSS();
