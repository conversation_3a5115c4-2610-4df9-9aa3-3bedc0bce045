<!-- Admin Users Index -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-users mr-2 text-indigo-600"></i>User Management
        </h1>
        <a href="/admin/users/create" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Add New User
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-users text-indigo-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= users.length %></h3>
                <p class="text-gray-600">Total Users</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-user-shield text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= users.filter(u => u.role === 'admin').length %></h3>
                <p class="text-gray-600">Admins</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-green-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= users.filter(u => new Date(u.createdAt) > new Date(Date.now() - 30*24*60*60*1000)).length %>
                </h3>
                <p class="text-gray-600">This Month</p>
            </div>
        </div>
    </div>
</div>


<!-- Users Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <% if (users && users.length > 0) { %>
        <table class="table admin-table w-full">
            <thead>
                <tr>
                    <th class="w-8">
                        <input type="checkbox" id="select-all" class="form-checkbox">
                    </th>
                    <th>User</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Created</th>
                    <th class="w-32">Actions</th>
                </tr>
            </thead>
            <tbody id="users-tbody">
                <% users.forEach(user => { %>
                    <tr class="user-row" data-user-id="<%= user._id %>">
                        <td>
                            <input type="checkbox" name="selected_items[]" value="<%= user._id %>" class="form-checkbox">
                        </td>
                        <td data-name="<%= user.name %>">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-indigo-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900"><%= user.name %></h3>
                                    <p class="text-sm text-gray-500">ID: <%= user._id.toString().substring(0, 8) %>...</p>
                                </div>
                            </div>
                        </td>
                        <td data-email="<%= user.email %>">
                            <span class="text-sm text-gray-900"><%= user.email %></span>
                        </td>
                        <td data-role="<%= user.role %>">
                            <span class="status-badge <%= user.role === 'admin' ? 'active' : 'draft' %>">
                                <i class="fas fa-<%= user.role === 'admin' ? 'user-shield' : 'user-edit' %> mr-1"></i>
                                <%= user.role.charAt(0).toUpperCase() + user.role.slice(1) %>
                            </span>
                        </td>
                        <td data-created="<%= user.createdAt %>">
                            <div class="text-sm">
                                <div class="text-gray-900"><%= new Date(user.createdAt).toLocaleDateString() %></div>
                                <div class="text-gray-500"><%= new Date(user.createdAt).toLocaleTimeString() %></div>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center space-x-2">
                                <button onclick="editUser('<%= user._id %>')" class="action-btn edit" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <% if (user._id.toString() !== user.id) { %>
                                    <button onclick="deleteItem('/admin/users/<%= user._id %>', 'user')" class="action-btn delete" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <% } else { %>
                                    <span class="text-xs text-gray-400">Current User</span>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>

        <!-- Bulk Actions -->
        <div id="bulk-actions" class="hidden p-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">
                    <span id="selected-count">0</span> users selected
                </span>
                <div class="flex space-x-2">
                    <button class="btn btn-secondary" onclick="changeRole()">
                        <i class="fas fa-user-cog mr-1"></i>Change Role
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelected()">
                        <i class="fas fa-trash mr-1"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    <% } else { %>
        <div class="p-12 text-center">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-users text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Users Found</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first user.</p>
            <a href="/admin/users/create" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Create First User
            </a>
        </div>
    <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-users');
    const roleFilter = document.getElementById('filter-role');
    const tbody = document.getElementById('users-tbody');
    const rows = Array.from(document.querySelectorAll('.user-row'));
    const selectAllCheckbox = document.getElementById('select-all');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Search and filter functionality
    function filterUsers() {
        const searchTerm = searchInput.value.toLowerCase();
        const roleValue = roleFilter.value;

        rows.forEach(row => {
            const name = row.querySelector('[data-name]').getAttribute('data-name').toLowerCase();
            const email = row.querySelector('[data-email]').getAttribute('data-email').toLowerCase();
            const role = row.querySelector('[data-role]').getAttribute('data-role');

            const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
            const matchesRole = !roleValue || role === roleValue;

            row.style.display = matchesSearch && matchesRole ? '' : 'none';
        });
    }

    // Bulk selection functionality
    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        const checkedBoxes = document.querySelectorAll('input[name="selected_items[]"]:checked');

        selectedCount.textContent = checkedBoxes.length;
        bulkActions.style.display = checkedBoxes.length > 0 ? 'block' : 'none';

        selectAllCheckbox.checked = checkboxes.length > 0 && checkedBoxes.length === checkboxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
    }

    // Event listeners
    searchInput.addEventListener('input', filterUsers);
    roleFilter.addEventListener('change', filterUsers);

    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    document.addEventListener('change', function(e) {
        if (e.target.name === 'selected_items[]') {
            updateBulkActions();
        }
    });

    // Initialize
    updateBulkActions();
});

function editUser(userId) {
    // Implementation for editing user
    console.log('Edit user:', userId);
    // Could open a modal or redirect to edit page
}

function changeRole() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    const newRole = prompt('Enter new role (admin/editor):');
    if (newRole && ['admin', 'editor'].includes(newRole)) {
        // Implementation would make AJAX calls to change role
        console.log('Change role to', newRole, 'for:', selectedIds);
    }
}

function deleteSelected() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} users?`)) {
        // Implementation would make AJAX calls to delete users
        console.log('Delete users:', selectedIds);
    }
}
</script>
