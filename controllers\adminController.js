const { User, Project, Resource, Event, Contact } = require('../models');
const bcrypt = require('bcrypt');
const apiClient = require('../utils/apiClient');

// Helper function to get auth token from session
const getAuthToken = (req) => {
  return req.session.authToken || req.session.user?.token;
};

// Helper function to get recent activity
async function getRecentActivity() {
  try {
    const activities = [];

    // Get recent projects
    const recentProjects = await Project.find().sort({ createdAt: -1 }).limit(3);
    recentProjects.forEach(project => {
      activities.push({
        type: 'project',
        action: 'created',
        title: `New project added: "${project.title}"`,
        time: project.createdAt,
        icon: 'fas fa-flask',
        color: 'blue'
      });
    });



    // Get recent events
    const recentEvents = await Event.find().sort({ createdAt: -1 }).limit(2);
    recentEvents.forEach(event => {
      activities.push({
        type: 'event',
        action: 'scheduled',
        title: `Event scheduled: "${event.title}"`,
        time: event.createdAt,
        icon: 'fas fa-calendar',
        color: 'yellow'
      });
    });

    // Get recent contacts
    const recentContacts = await Contact.find().sort({ createdAt: -1 }).limit(2);
    recentContacts.forEach(contact => {
      activities.push({
        type: 'contact',
        action: 'received',
        title: `New contact message from ${contact.name}`,
        time: contact.createdAt,
        icon: 'fas fa-envelope',
        color: 'red'
      });
    });

    // Sort by time and return latest 8
    return activities.sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 8);
  } catch (error) {
    console.error('Error getting recent activity:', error);
    return [];
  }
}

// Helper function to get notifications
async function getNotifications() {
  try {
    const notifications = [];

    // Unread messages
    const unreadCount = await Contact.countDocuments({ isRead: false });
    if (unreadCount > 0) {
      notifications.push({
        type: 'message',
        title: `${unreadCount} new message${unreadCount > 1 ? 's' : ''}`,
        description: 'You have unread contact messages',
        time: new Date(),
        icon: 'fas fa-envelope',
        color: 'red',
        link: '/admin/contacts'
      });
    }

    // Upcoming events (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const upcomingEvents = await Event.find({
      date: { $gte: new Date(), $lte: nextWeek },
      isPast: false
    });

    if (upcomingEvents.length > 0) {
      notifications.push({
        type: 'event',
        title: `${upcomingEvents.length} upcoming event${upcomingEvents.length > 1 ? 's' : ''}`,
        description: 'Events scheduled for this week',
        time: new Date(),
        icon: 'fas fa-calendar',
        color: 'yellow',
        link: '/admin/events'
      });
    }

    // Recent user registrations (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const newUsers = await User.countDocuments({ createdAt: { $gte: yesterday } });

    if (newUsers > 0) {
      notifications.push({
        type: 'user',
        title: `${newUsers} new user${newUsers > 1 ? 's' : ''}`,
        description: 'New users registered in the last 24 hours',
        time: new Date(),
        icon: 'fas fa-user-plus',
        color: 'blue',
        link: '/admin/users'
      });
    }

    return notifications.slice(0, 5); // Return max 5 notifications
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
}

// Middleware to check if user is authenticated
exports.requireAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    // Make user data available to all admin views
    res.locals.user = req.session.user;
    return next();
  } else {
    return res.redirect('/admin/login');
  }
};

// Middleware to check if user is admin
exports.requireAdmin = (req, res, next) => {
  if (req.session && req.session.user && req.session.user.role === 'admin') {
    return next();
  } else {
    return res.status(403).render('pages/error', {
      title: 'Access Denied',
      error: 'You do not have permission to access this page.'
    });
  }
};

// Get login page
exports.getLoginPage = (req, res) => {
  // Redirect to dashboard if already logged in
  if (req.session && req.session.user) {
    return res.redirect('/admin/dashboard');
  }

  res.render('admin/auth/login', {
    title: 'Admin Login',
    layout: false, // Use a different layout for login page
    error: null
  });
};

// Handle login
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Please provide both email and password.'
      });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Invalid email or password.'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Invalid email or password.'
      });
    }

    // Create session
    req.session.user = {
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role
    };

    res.redirect('/admin/dashboard');
  } catch (error) {
    console.error('Error during login:', error);
    res.render('admin/auth/login', {
      title: 'Admin Login',
      layout: false,
      error: 'An error occurred during login. Please try again.'
    });
  }
};

// Handle logout
exports.logout = (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Error during logout:', err);
    }
    res.redirect('/admin/login');
  });
};

// Get admin dashboard
exports.getDashboard = async (req, res) => {
  try {
    // Get current date for comparisons
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get statistics using API calls with fallback to direct DB access
    const token = getAuthToken(req);

    // Try to get data via API first, fallback to direct DB access
    let stats = {
      projects: 0,
      resources: 0,
      events: 0,
      users: 0,
      contacts: 0,
      unreadContacts: 0
    };

    // Use direct database access for admin dashboard (more reliable)
    stats = {
      projects: await Project.countDocuments(),
      resources: await Resource.countDocuments(),
      events: await Event.countDocuments(),
      users: await User.countDocuments(),
      contacts: await Contact.countDocuments(),
      unreadContacts: await Contact.countDocuments({ isRead: false })
    };

    // Get previous month stats for percentage calculation (direct DB access for admin)
    const prevStats = {
      projects: await Project.countDocuments({ createdAt: { $lt: lastMonth } }),
      resources: await Resource.countDocuments({ createdAt: { $lt: lastMonth } }),
      events: await Event.countDocuments({ createdAt: { $lt: lastMonth } }),
      users: await User.countDocuments({ createdAt: { $lt: lastMonth } }),
      contacts: await Contact.countDocuments({ createdAt: { $lt: lastMonth } })
    };

    // Calculate percentage changes
    const percentageChanges = {};
    Object.keys(stats).forEach(key => {
      if (key !== 'unreadContacts' && prevStats[key] !== undefined) {
        const current = stats[key];
        const previous = prevStats[key];
        if (previous === 0) {
          percentageChanges[key] = current > 0 ? 100 : 0;
        } else {
          percentageChanges[key] = Math.round(((current - previous) / previous) * 100);
        }
      }
    });

    // Get recent items using direct database access (more reliable for admin)
    const recentProjects = await Project.find().sort({ createdAt: -1 }).limit(5);
    const upcomingEvents = await Event.find({
      date: { $gte: new Date() },
      isPast: false
    }).sort({ date: 1 }).limit(5);

    // Always use direct DB access for admin-specific data
    const recentContacts = await Contact.find().sort({ createdAt: -1 }).limit(5);

    // Get real recent activity
    const recentActivity = await getRecentActivity();

    // Get notifications
    const notifications = await getNotifications();

    res.render('admin/dashboard', {
      title: 'Admin Dashboard',
      user: req.session.user,
      currentPage: 'dashboard',
      stats,
      percentageChanges,
      recentProjects,
      recentContacts,
      upcomingEvents,
      recentActivity,
      notifications
    });
  } catch (error) {
    console.error('Error loading dashboard:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'An error occurred while loading the dashboard.'
    });
  }
};

// Get user management page
exports.getUserManagement = async (req, res) => {
  try {
    const users = await User.find().sort({ createdAt: -1 });

    res.render('admin/users/index', {
      title: 'Admin - User Management',
      users,
      currentPage: 'users'
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'An error occurred while fetching users.'
    });
  }
};

// Get form to create a new user
exports.getCreateUserForm = (req, res) => {
  res.render('admin/users/create', {
    title: 'Admin - Create New User',
    currentPage: 'users'
  });
};

// Create a new user
exports.createUser = async (req, res) => {
  try {
    const { name, email, password, role } = req.body;

    // Validate input
    if (!name || !email || !password) {
      return res.render('admin/users/create', {
        title: 'Admin - Create New User',
        error: 'Please fill in all required fields.',
        formData: req.body,
        currentPage: 'users'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.render('admin/users/create', {
        title: 'Admin - Create New User',
        error: 'A user with this email already exists.',
        formData: req.body,
        currentPage: 'users'
      });
    }

    // Create new user
    const newUser = new User({
      name,
      email: email.toLowerCase(),
      password,
      role: role || 'admin'
    });

    await newUser.save();

    res.redirect('/admin/users');
  } catch (error) {
    console.error('Error creating user:', error);
    res.render('admin/users/create', {
      title: 'Admin - Create New User',
      error: 'An error occurred while creating the user.',
      formData: req.body,
      currentPage: 'users'
    });
  }
};

// Delete a user
exports.deleteUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Prevent deleting own account
    if (userId === req.session.user.id) {
      return res.status(400).json({ error: 'You cannot delete your own account.' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ error: 'User not found.' });
    }

    await User.findByIdAndDelete(userId);

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'An error occurred while deleting the user.' });
  }
};

// API endpoint to get notifications
exports.getNotificationsAPI = async (req, res) => {
  try {
    const notifications = await getNotifications();
    res.json({ notifications, count: notifications.length });
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(500).json({ error: 'Failed to get notifications' });
  }
};

// API endpoint to mark notification as read
exports.markNotificationRead = async (req, res) => {
  try {
    const { type, id } = req.body;

    if (type === 'message' && id) {
      await Contact.findByIdAndUpdate(id, { isRead: true });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ error: 'Failed to mark notification as read' });
  }
};