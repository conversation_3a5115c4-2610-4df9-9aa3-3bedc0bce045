# Science Club Website

A complete, responsive Science Club website built with Node.js, Express.js, MongoDB, EJS, and Tailwind CSS. The website promotes student research, shares science news and resources, and connects with the community through events and contact forms.

## 🚀 Features

### Public Features
- **Home Page**: Hero section with featured projects, latest blog posts, and upcoming events
- **About Page**: Information about the Science Club, mission, values, and team
- **Research Projects**: Showcase of student research with detailed project pages
- **Science Blog**: Latest science news, articles, and research findings
- **Resources**: Categorized collection of research tools, papers, and educational materials
- **Events**: Upcoming workshops, seminars, and conferences
- **Contact Form**: Contact form with email notifications

### Admin Features
- **Admin Dashboard**: Overview of all content with statistics and quick actions
- **Content Management**: Full CRUD operations for projects, blog posts, resources, and events
- **User Management**: Admin user creation and management
- **File Uploads**: Support for images and PDF files
- **Contact Management**: View and manage contact form submissions
- **Authentication**: Secure admin login with session management

### Technical Features
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **File Upload**: Image and PDF upload with validation
- **Search & Filter**: Advanced search and filtering capabilities
- **Email Integration**: Contact form notifications via Nodemailer
- **Database**: MongoDB with Mongoose ODM
- **Security**: Password hashing, session management, and input validation

## 🛠️ Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose
- **Templating**: EJS (Embedded JavaScript)
- **Styling**: Tailwind CSS
- **File Upload**: Multer
- **Authentication**: bcrypt, express-session
- **Email**: Nodemailer
- **Icons**: Font Awesome

## 📦 Installation

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scienceclub
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your configuration:
   ```env
   MONGODB_URI=mongodb://localhost:27017/scienceclub
   PORT=3000
   SESSION_SECRET=your-super-secret-session-key
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ```

4. **Start MongoDB**
   - For local MongoDB: `mongod`
   - For MongoDB Atlas: Ensure your connection string is correct in `.env`

5. **Seed the database** (Optional but recommended)
   ```bash
   npm run seed
   ```
   This creates sample data and a default admin user:
   - Email: `<EMAIL>`
   - Password: `admin123`

6. **Start the application**
   ```bash
   # Development mode with auto-restart
   npm run dev

   # Production mode
   npm start
   ```

7. **Access the application**
   - Website: http://localhost:3000
   - Admin Panel: http://localhost:3000/admin/login
- **About Page**: Information about the Science Club, mission, and members
- **Research Projects**: Display student research projects with images and descriptions
- **Science News/Blog**: Articles and blog posts about science topics
- **Resources Page**: Categorized list of resources (PDFs, links, videos)
- **Events Page**: Upcoming and past events with details
- **Contact/Join Us**: Form for contacting the club or joining
- **Admin Panel**: Dashboard to manage content with authentication

## Tech Stack

- **Backend**: Node.js with Express.js
- **Database**: MongoDB (via Mongoose)
- **Templating Engine**: EJS
- **Frontend**: Tailwind CSS
- **Authentication**: express-session with bcrypt
- **File Uploads**: Multer
- **Email**: Nodemailer

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd scienceclub
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file in the root directory (use `.env.example` as a template):
   ```
   PORT=3000
   MONGODB_URI=mongodb://localhost:27017/scienceclub
   SESSION_SECRET=your-secret-key
   NODE_ENV=development
   ```

4. Start MongoDB (make sure MongoDB is installed and running on your system)

5. Run the application:
   - For development:
     ```
     npm run dev
     ```
   - For production:
     ```
     npm start
     ```

6. Open your browser and navigate to `http://localhost:3000`

## Project Structure

```
science-club-website/
├── public/               # Static files
│   ├── css/              # CSS files
│   ├── js/               # JavaScript files
│   └── uploads/          # Uploaded files
├── views/                # EJS templates
│   ├── partials/         # Reusable template parts
│   ├── pages/            # Page templates
│   └── admin/            # Admin panel templates
├── models/               # Mongoose models
├── routes/               # Express routes
├── controllers/          # Route controllers
├── app.js                # Main application file
├── .env                  # Environment variables
├── package.json          # Project dependencies
└── README.md             # Project documentation
```

## Deployment

This application can be deployed on platforms like Render, Railway, or Heroku:

1. Create an account on your preferred platform
2. Connect your repository
3. Set the environment variables
4. Deploy the application

## License

ISC
