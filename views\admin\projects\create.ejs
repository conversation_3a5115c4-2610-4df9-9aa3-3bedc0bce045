<!-- Create Project Form -->
<div class="admin-header">
    <h1 class="admin-title">
        <i class="fas fa-flask mr-3"></i>Create New Project
    </h1>
    <p class="text-slate-200 mt-2">Add a new science project to showcase your research and experiments</p>
</div>

<div class="form-container">
    <form action="/admin/projects/create" method="POST" enctype="multipart/form-data">
        <!-- Basic Information Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-info-circle"></i>Basic Information
            </h3>

            <div class="form-grid">
                <div class="form-group">
                    <label for="title" class="form-label required">Project Title</label>
                    <input type="text" id="title" name="title" class="form-input" required
                           placeholder="Enter an engaging project title">
                    <p class="form-help">Choose a clear, descriptive title that captures the essence of your project</p>
                </div>

                <div class="form-group">
                    <label for="authors" class="form-label required">Authors</label>
                    <input type="text" id="authors" name="authors" class="form-input" required
                           placeholder="<PERSON> Doe, <PERSON> Smith, Dr. <PERSON>">
                    <p class="form-help">List all contributors separated by commas</p>
                </div>
            </div>

            <div class="form-group">
                <label for="description" class="form-label required">Project Description</label>
                <textarea id="description" name="description" rows="6" class="form-textarea" required
                          placeholder="Provide a comprehensive description of your project, including objectives, methodology, and expected outcomes..."></textarea>
                <p class="form-help">Describe your project's goals, methods, and significance in detail</p>
            </div>

            <div class="form-group">
                <label for="tags" class="form-label">Research Tags</label>
                <input type="text" id="tags" name="tags" class="form-input"
                       placeholder="chemistry, experiment, analysis, research">
                <p class="form-help">Add relevant tags to help categorize and discover your project</p>
            </div>
        </div>

        <!-- Media & Files Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-images"></i>Media & Documentation
            </h3>

            <div class="form-grid">
                <div class="form-group">
                    <label for="image" class="form-label">Project Image</label>
                    <input type="file" id="image" name="image" accept="image/*" class="form-input">
                    <p class="form-help">Upload a high-quality image that represents your project (JPG, PNG, GIF up to 5MB)</p>
                </div>

                <div class="form-group">
                    <label for="pdfFile" class="form-label">Project Documentation</label>
                    <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" class="form-input">
                    <p class="form-help">Upload detailed project documentation, research paper, or report (PDF up to 10MB)</p>
                </div>
            </div>
        </div>

        <!-- Project Settings Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-cog"></i>Project Settings
            </h3>

            <div class="checkbox-wrapper">
                <input type="checkbox" id="featured" name="featured" value="true">
                <label for="featured" class="font-medium text-gray-700">
                    <span class="text-lg">⭐</span> Mark as Featured Project
                    <p class="text-sm text-gray-500 mt-1">Featured projects appear prominently on the homepage</p>
                </label>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/admin/projects" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>Create Project
            </button>
        </div>
    </form>
</div>

<!-- Simple Form Script -->
<script>
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const authors = document.getElementById('authors').value.trim();

        if (!title || !description || !authors) {
            e.preventDefault();
            alert('Please fill in all required fields (Title, Description, Authors)');
        }
    });
</script>