#!/usr/bin/env node

/**
 * Test Search Functionality
 * Tests the search API endpoints to ensure they're working correctly
 */

const dotenv = require('dotenv');
const apiClient = require('../utils/apiClient');

// Load environment variables
dotenv.config();

async function testSearchFunctionality() {
  console.log('🔍 Testing Search Functionality...\n');
  
  try {
    // Test 1: Basic Search
    console.log('1. Testing Basic Search...');
    const basicSearch = await apiClient.search('test');
    console.log('   ✅ Basic Search:', basicSearch.success ? 'PASS' : 'FAIL');
    if (basicSearch.success) {
      console.log(`   📊 Found ${basicSearch.data.total || 0} results`);
    } else {
      console.log('   ❌ Error:', basicSearch.message);
    }
    
    // Test 2: Search with Type Filter
    console.log('\n2. Testing Search with Type Filter...');
    const typeSearch = await apiClient.search('test', { type: 'projects' });
    console.log('   ✅ Type Filter Search:', typeSearch.success ? 'PASS' : 'FAIL');
    if (typeSearch.success) {
      console.log(`   📊 Found ${typeSearch.data.total || 0} project results`);
    } else {
      console.log('   ❌ Error:', typeSearch.message);
    }
    
    // Test 3: Search Suggestions
    console.log('\n3. Testing Search Suggestions...');
    try {
      const response = await fetch(`http://localhost:${process.env.PORT || 3000}/api/search/suggestions?q=test`);
      const suggestions = await response.json();
      console.log('   ✅ Search Suggestions:', suggestions.success ? 'PASS' : 'FAIL');
      if (suggestions.success) {
        console.log(`   📊 Found ${suggestions.data.length} suggestions`);
      } else {
        console.log('   ❌ Error:', suggestions.message);
      }
    } catch (error) {
      console.log('   ❌ Suggestions Error:', error.message);
    }
    
    // Test 4: Popular Terms
    console.log('\n4. Testing Popular Terms...');
    try {
      const response = await fetch(`http://localhost:${process.env.PORT || 3000}/api/search/popular`);
      const popular = await response.json();
      console.log('   ✅ Popular Terms:', popular.success ? 'PASS' : 'FAIL');
      if (popular.success) {
        console.log(`   📊 Found ${popular.data.length} popular terms`);
      } else {
        console.log('   ❌ Error:', popular.message);
      }
    } catch (error) {
      console.log('   ❌ Popular Terms Error:', error.message);
    }
    
    // Test 5: Search Filters
    console.log('\n5. Testing Search Filters...');
    try {
      const response = await fetch(`http://localhost:${process.env.PORT || 3000}/api/search/filters`);
      const filters = await response.json();
      console.log('   ✅ Search Filters:', filters.success ? 'PASS' : 'FAIL');
      if (filters.success) {
        console.log(`   📊 Categories: ${filters.data.categories?.length || 0}`);
        console.log(`   📊 Tags: ${filters.data.tags?.length || 0}`);
        console.log(`   📊 Authors: <AUTHORS>
      } else {
        console.log('   ❌ Error:', filters.message);
      }
    } catch (error) {
      console.log('   ❌ Filters Error:', error.message);
    }
    
    // Test 6: Search Analytics
    console.log('\n6. Testing Search Analytics...');
    try {
      const response = await fetch(`http://localhost:${process.env.PORT || 3000}/api/search/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'test',
          type: 'all',
          resultsCount: 5
        })
      });
      const analytics = await response.json();
      console.log('   ✅ Search Analytics:', analytics.success ? 'PASS' : 'FAIL');
      if (!analytics.success) {
        console.log('   ❌ Error:', analytics.message);
      }
    } catch (error) {
      console.log('   ❌ Analytics Error:', error.message);
    }
    
    console.log('\n🎉 Search Functionality Test Complete!');
    
  } catch (error) {
    console.error('\n❌ Test Failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testSearchFunctionality().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Test script error:', error);
    process.exit(1);
  });
}

module.exports = testSearchFunctionality;
