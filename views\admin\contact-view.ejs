<%- contentFor('title') %>
<%= title %>
<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div class="flex items-center">
            <a 
                href="/admin/contacts" 
                class="text-blue-600 hover:text-blue-800 mr-4"
                title="Back to Contacts"
            >
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Contact Message</h1>
                <p class="text-gray-600 mt-1">
                    Received on <%= new Date(contact.createdAt).toLocaleDateString() %> at <%= new Date(contact.createdAt).toLocaleTimeString() %>
                </p>
            </div>
        </div>
        
        <div class="flex items-center space-x-3">
            <!-- Read Status -->
            <% if (contact.isRead) { %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <i class="fas fa-check-circle mr-2"></i>
                    Read
                </span>
            <% } else { %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <i class="fas fa-envelope mr-2"></i>
                    Unread
                </span>
            <% } %>

            <!-- Actions -->
            <button 
                onclick="toggleRead('<%= contact._id %>', <%= contact.isRead %>)"
                class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                title="<%= contact.isRead ? 'Mark as Unread' : 'Mark as Read' %>"
            >
                <i class="fas fa-<%= contact.isRead ? 'envelope' : 'envelope-open' %> mr-2"></i>
                <%= contact.isRead ? 'Mark Unread' : 'Mark Read' %>
            </button>

            <form 
                action="/admin/contacts/<%= contact._id %>/delete" 
                method="POST" 
                class="inline"
                onsubmit="return confirm('Are you sure you want to delete this message?')"
            >
                <button 
                    type="submit" 
                    class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                    <i class="fas fa-trash mr-2"></i>
                    Delete
                </button>
            </form>
        </div>
    </div>

    <!-- Contact Details -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Message Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Message</h2>
                
                <!-- Subject -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <p class="text-lg text-gray-900 bg-gray-50 p-3 rounded-lg">
                        <%= contact.subject %>
                    </p>
                </div>

                <!-- Message -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-gray-900 whitespace-pre-wrap leading-relaxed">
                            <%= contact.message %>
                        </p>
                    </div>
                </div>

                <!-- Quick Reply -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Reply</h3>
                    <a 
                        href="mailto:<%= contact.email %>?subject=Re: <%= encodeURIComponent(contact.subject) %>&body=Hi <%= contact.name %>,%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards,%0D%0AScience Club Team"
                        class="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <i class="fas fa-reply mr-2"></i>
                        Reply via Email
                    </a>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="space-y-6">
            <!-- Contact Details -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Contact Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="text-gray-900 mt-1"><%= contact.name %></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="text-gray-900 mt-1">
                            <a href="mailto:<%= contact.email %>" class="text-blue-600 hover:text-blue-800">
                                <%= contact.email %>
                            </a>
                        </p>
                    </div>

                    <% if (contact.phone) { %>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <p class="text-gray-900 mt-1">
                                <a href="tel:<%= contact.phone %>" class="text-blue-600 hover:text-blue-800">
                                    <%= contact.phone %>
                                </a>
                            </p>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Message Details -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Message Details</h3>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Received:</span>
                        <span class="text-gray-900">
                            <%= new Date(contact.createdAt).toLocaleDateString() %>
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Time:</span>
                        <span class="text-gray-900">
                            <%= new Date(contact.createdAt).toLocaleTimeString() %>
                        </span>
                    </div>

                    <% if (contact.readAt) { %>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Read:</span>
                            <span class="text-gray-900">
                                <%= new Date(contact.readAt).toLocaleDateString() %>
                            </span>
                        </div>
                    <% } %>

                    <div class="flex justify-between">
                        <span class="text-gray-600">Status:</span>
                        <span class="<%= contact.isRead ? 'text-green-600' : 'text-yellow-600' %>">
                            <%= contact.isRead ? 'Read' : 'Unread' %>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRead(contactId, isCurrentlyRead) {
    fetch(`/admin/contacts/${contactId}/toggle-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status');
    });
}
</script>
