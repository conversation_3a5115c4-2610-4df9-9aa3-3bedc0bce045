<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Science Club API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .card h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Science Club API Demo</h1>
        <p>This page demonstrates the Science Club API functionality. Click the buttons below to test different endpoints.</p>
        
        <div class="grid">
            <div class="card">
                <h3>🔍 API Health</h3>
                <button class="button" onclick="testHealth()">Check API Health</button>
                <div id="health-result" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📚 Featured Projects</h3>
                <button class="button" onclick="getFeaturedProjects()">Get Featured Projects</button>
                <div id="projects-result" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📅 Upcoming Events</h3>
                <button class="button" onclick="getUpcomingEvents()">Get Upcoming Events</button>
                <div id="events-result" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>🔍 Search</h3>
                <input type="text" id="search-input" placeholder="Search for..." style="width: 200px; padding: 8px; margin-right: 10px;">
                <button class="button" onclick="searchContent()">Search</button>
                <div id="search-result" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📧 Contact Form</h3>
                <input type="text" id="contact-name" placeholder="Your Name" style="width: 100%; padding: 8px; margin: 5px 0;">
                <input type="email" id="contact-email" placeholder="Your Email" style="width: 100%; padding: 8px; margin: 5px 0;">
                <textarea id="contact-message" placeholder="Your Message" style="width: 100%; padding: 8px; margin: 5px 0; height: 80px;"></textarea>
                <button class="button" onclick="submitContact()">Submit Contact</button>
                <div id="contact-result" class="result" style="display: none;"></div>
            </div>

            <div class="card">
                <h3>📖 API Documentation</h3>
                <p>View the complete API documentation:</p>
                <button class="button" onclick="window.open('/api/docs', '_blank')">Open API Docs</button>
            </div>
        </div>
    </div>

    <script src="/js/api-example.js"></script>
    <script>
        // Initialize API client
        const api = new ScienceClubAPI();

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function testHealth() {
            try {
                const result = await api.healthCheck();
                showResult('health-result', result, true);
            } catch (error) {
                showResult('health-result', { error: error.message }, false);
            }
        }

        async function getFeaturedProjects() {
            try {
                const result = await api.getFeaturedProjects(6);
                showResult('projects-result', result, true);
            } catch (error) {
                showResult('projects-result', { error: error.message }, false);
            }
        }

        async function getUpcomingEvents() {
            try {
                const result = await api.getUpcomingEvents(6);
                showResult('events-result', result, true);
            } catch (error) {
                showResult('events-result', { error: error.message }, false);
            }
        }

        async function searchContent() {
            const query = document.getElementById('search-input').value;
            if (!query) {
                alert('Please enter a search term');
                return;
            }

            try {
                const result = await api.search(query, { limit: 5 });
                showResult('search-result', result, true);
            } catch (error) {
                showResult('search-result', { error: error.message }, false);
            }
        }

        async function submitContact() {
            const name = document.getElementById('contact-name').value;
            const email = document.getElementById('contact-email').value;
            const message = document.getElementById('contact-message').value;

            if (!name || !email || !message) {
                alert('Please fill in all fields');
                return;
            }

            try {
                const result = await api.submitContact({
                    name,
                    email,
                    subject: 'API Demo Contact',
                    message
                });
                showResult('contact-result', result, true);
                
                // Clear form
                document.getElementById('contact-name').value = '';
                document.getElementById('contact-email').value = '';
                document.getElementById('contact-message').value = '';
            } catch (error) {
                showResult('contact-result', { error: error.message }, false);
            }
        }

        // Auto-test health on page load
        window.addEventListener('load', () => {
            testHealth();
        });
    </script>
</body>
</html>
