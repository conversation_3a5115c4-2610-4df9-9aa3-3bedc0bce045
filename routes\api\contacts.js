const express = require('express');
const router = express.Router();
const Contact = require('../../models/Contact');
const ApiResponse = require('../../utils/apiResponse');
const { verifyToken, requireAdmin } = require('../../middleware/apiAuth');
const { contactLimiter } = require('../../middleware/rateLimiter');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

/**
 * @route   POST /api/contacts
 * @desc    Submit contact form
 * @access  Public
 */
router.post('/',
  contactLimiter,
  validationChains.createContact,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { name, email, phone, subject, message } = req.body;

      const contact = new Contact({
        name,
        email,
        phone,
        subject: subject || 'Contact Form Submission',
        message
      });

      await contact.save();

      // Return contact data without sensitive information
      const contactData = {
        id: contact._id,
        name: contact.name,
        email: contact.email,
        subject: contact.subject,
        message: contact.message,
        createdAt: contact.createdAt
      };

      ApiResponse.success(res, contactData, 'Contact form submitted successfully', 201);

    } catch (error) {
      console.error('Submit contact error:', error);
      ApiResponse.error(res, 'Failed to submit contact form');
    }
  }
);

/**
 * @route   GET /api/contacts
 * @desc    Get all contacts with pagination
 * @access  Private (Admin only)
 */
router.get('/',
  verifyToken,
  requireAdmin,
  validationChains.getPaginated,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query
      const query = {};

      // Filter by read status
      if (req.query.isRead !== undefined) {
        query.isRead = req.query.isRead === 'true';
      }

      // Search by name, email, or subject
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { email: { $regex: req.query.search, $options: 'i' } },
          { subject: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const contacts = await Contact.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Contact.countDocuments(query);
      const totalPages = Math.ceil(total / limit);

      const pagination = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      ApiResponse.paginated(res, contacts, pagination, 'Contacts retrieved successfully');

    } catch (error) {
      console.error('Get contacts error:', error);
      ApiResponse.error(res, 'Failed to retrieve contacts');
    }
  }
);

/**
 * @route   GET /api/contacts/stats/summary
 * @desc    Get contact statistics
 * @access  Private (Admin only)
 */
router.get('/stats/summary',
  verifyToken,
  requireAdmin,
  async (req, res) => {
    try {
      const totalContacts = await Contact.countDocuments();
      const unreadContacts = await Contact.countDocuments({ isRead: false });
      const readContacts = await Contact.countDocuments({ isRead: true });

      // Get contacts from last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentContacts = await Contact.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
      });

      const stats = {
        total: totalContacts,
        unread: unreadContacts,
        read: readContacts,
        recent: recentContacts
      };

      ApiResponse.success(res, stats, 'Contact statistics retrieved successfully');

    } catch (error) {
      console.error('Get contact stats error:', error);
      ApiResponse.error(res, 'Failed to retrieve contact statistics');
    }
  }
);

/**
 * @route   GET /api/contacts/:id
 * @desc    Get contact by ID
 * @access  Private (Admin only)
 */
router.get('/:id',
  verifyToken,
  requireAdmin,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return ApiResponse.notFound(res, 'Contact not found');
      }

      ApiResponse.success(res, contact, 'Contact retrieved successfully');

    } catch (error) {
      console.error('Get contact error:', error);
      ApiResponse.error(res, 'Failed to retrieve contact');
    }
  }
);

/**
 * @route   PATCH /api/contacts/:id/read
 * @desc    Mark contact as read/unread
 * @access  Private (Admin only)
 */
router.patch('/:id/read',
  verifyToken,
  requireAdmin,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return ApiResponse.notFound(res, 'Contact not found');
      }

      // Toggle read status or set to specific value
      if (req.body.isRead !== undefined) {
        contact.isRead = req.body.isRead;
      } else {
        contact.isRead = !contact.isRead;
      }

      contact.readAt = contact.isRead ? new Date() : null;
      await contact.save();

      ApiResponse.success(res, {
        id: contact._id,
        isRead: contact.isRead,
        readAt: contact.readAt
      }, 'Contact read status updated successfully');

    } catch (error) {
      console.error('Update contact read status error:', error);
      ApiResponse.error(res, 'Failed to update contact read status');
    }
  }
);

/**
 * @route   DELETE /api/contacts/:id
 * @desc    Delete contact
 * @access  Private (Admin only)
 */
router.delete('/:id',
  verifyToken,
  requireAdmin,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return ApiResponse.notFound(res, 'Contact not found');
      }

      await Contact.findByIdAndDelete(req.params.id);

      ApiResponse.success(res, null, 'Contact deleted successfully');

    } catch (error) {
      console.error('Delete contact error:', error);
      ApiResponse.error(res, 'Failed to delete contact');
    }
  }
);

module.exports = router;
