const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { Project, Event, Resource } = require('../models');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/scienceclub');
    console.log('MongoDB connected successfully');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

const testData = async () => {
  try {
    console.log('Testing data retrieval...\n');

    // Test Projects
    console.log('=== PROJECTS ===');
    const projects = await Project.find({}).sort({ createdAt: -1 });
    console.log(`Found ${projects.length} projects:`);
    projects.forEach(project => {
      console.log(`- ${project.title} (Featured: ${project.featured})`);
    });
    console.log('');



    // Test Events
    console.log('=== EVENTS ===');
    const events = await Event.find({}).sort({ date: 1 });
    console.log(`Found ${events.length} events:`);
    events.forEach(event => {
      console.log(`- ${event.title} (Date: ${event.date.toDateString()}, Featured: ${event.featured})`);
    });
    console.log('');

    // Test Resources
    console.log('=== RESOURCES ===');
    const resources = await Resource.find({}).sort({ createdAt: -1 });
    console.log(`Found ${resources.length} resources:`);
    resources.forEach(resource => {
      console.log(`- ${resource.title} (Type: ${resource.type}, Category: ${resource.category})`);
    });
    console.log('');

    console.log('✅ All data retrieval tests completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing data:', error);
    process.exit(1);
  }
};

// Run the test
connectDB().then(() => {
  testData();
});
