const express = require('express');
const router = express.Router();
const User = require('../../models/User');
const ApiResponse = require('../../utils/apiResponse');
const { generateToken, verifyToken } = require('../../middleware/apiAuth');
const { authLimiter } = require('../../middleware/rateLimiter');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

// Apply auth rate limiting
router.use(authLimiter);

/**
 * @route   POST /api/auth/login
 * @desc    Login user and get access token
 * @access  Public
 */
router.post('/login', 
  validationChains.login,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email, password } = req.body;

      // Find user by email
      const user = await User.findOne({ email: email.toLowerCase() });
      
      if (!user) {
        return ApiResponse.unauthorized(res, 'Invalid email or password');
      }

      // Check password
      const isPasswordValid = await user.comparePassword(password);
      
      if (!isPasswordValid) {
        return ApiResponse.unauthorized(res, 'Invalid email or password');
      }

      // Generate token
      const token = generateToken(user);

      // Return user data (without password) and token
      const userData = {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt
      };

      ApiResponse.success(res, {
        user: userData,
        token,
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
      }, 'Login successful');

    } catch (error) {
      console.error('Login error:', error);
      ApiResponse.error(res, 'Login failed');
    }
  }
);

/**
 * @route   POST /api/auth/verify
 * @desc    Verify token and get user data
 * @access  Private
 */
router.post('/verify', verifyToken, async (req, res) => {
  try {
    const userData = {
      id: req.user._id,
      name: req.user.name,
      email: req.user.email,
      role: req.user.role,
      createdAt: req.user.createdAt
    };

    ApiResponse.success(res, { user: userData }, 'Token is valid');
  } catch (error) {
    console.error('Token verification error:', error);
    ApiResponse.error(res, 'Token verification failed');
  }
});

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Private
 */
router.post('/refresh', verifyToken, async (req, res) => {
  try {
    // Generate new token
    const newToken = generateToken(req.user);

    ApiResponse.success(res, {
      token: newToken,
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    }, 'Token refreshed successfully');

  } catch (error) {
    console.error('Token refresh error:', error);
    ApiResponse.error(res, 'Token refresh failed');
  }
});

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token removal)
 * @access  Private
 */
router.post('/logout', verifyToken, (req, res) => {
  // Since we're using stateless JWT tokens, logout is handled client-side
  // by removing the token from storage
  ApiResponse.success(res, null, 'Logout successful');
});

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', verifyToken, async (req, res) => {
  try {
    const userData = {
      id: req.user._id,
      name: req.user.name,
      email: req.user.email,
      role: req.user.role,
      createdAt: req.user.createdAt
    };

    ApiResponse.success(res, { user: userData }, 'User profile retrieved');
  } catch (error) {
    console.error('Get profile error:', error);
    ApiResponse.error(res, 'Failed to get user profile');
  }
});

module.exports = router;
