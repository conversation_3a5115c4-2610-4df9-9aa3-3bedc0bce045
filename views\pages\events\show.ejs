<!-- Event Detail Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-br from-green-600 via-teal-600 to-blue-700 text-black py-12 sm:py-16 lg:py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;grid&quot; width=&quot;10&quot; height=&quot;10&quot; patternUnits=&quot;userSpaceOnUse&quot;><path d=&quot;M 10 0 L 0 0 0 10&quot; fill=&quot;none&quot; stroke=&quot;white&quot; stroke-width=&quot;0.5&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23grid)&quot;/></svg>');"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">


        <div class="max-w-5xl">


            <!-- Event Status -->
            <div class="mb-4 sm:mb-6">
                <div class="categories">
                    <%
                    const eventDate = new Date(event.date);
                    const now = new Date();
                    const isUpcoming = eventDate > now;
                    const isPast = eventDate < now;
                    const isToday = eventDate.toDateString() === now.toDateString();
                    %>

                    <% if (isToday) { %>
                        <span class="category bg-red-500 text-black animate-pulse">
                            <i class="fas fa-circle mr-1"></i>Happening Today
                        </span>
                    <% } else if (isUpcoming) { %>
                        <span class="category bg-green-500 text-black">
                            <i class="fas fa-calendar-check mr-1"></i>Upcoming Event
                        </span>
                    <% } else { %>
                        <span class="category bg-gray-500 text-black">
                            <i class="fas fa-history mr-1"></i>Past Event
                        </span>
                    <% } %>

                    <% if (event.category) { %>
                        <span class="category bg-white bg-opacity-20 text-black">
                            <%= event.category %>
                        </span>
                    <% } %>
                </div>
            </div>

            <!-- Title -->
            <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
                <%= event.title %>
            </h1>



            <!-- Enhanced Sidebar -->
            <div class="lg:col-span-1 order-1 lg:order-2">
                <div class="sticky top-4 lg:top-8 space-y-6">

                 <!-- Event Statistics -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
        <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
        Event Info
    </h3>

    <div class="space-y-4">

        <!-- Event Date with Day Name -->
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-calendar-plus text-blue-500 mr-2"></i>
                <span class="text-gray-600 text-sm">Event Date</span>
            </div>
            <span class="font-semibold text-gray-900 text-sm">
                <%= new Date(event.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' }) %>
            </span>
        </div>

        <!-- Start Time -->
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-clock text-green-500 mr-2"></i>
                <span class="text-gray-600 text-sm">Start Time</span>
            </div>
            <span class="font-semibold text-gray-900 text-sm">
                <%= new Date(event.date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) %>
            </span>
        </div>

        <!-- Full Location -->
        <% if (event.location) { %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                    <span class="text-gray-600 text-sm">Location</span>
                </div>
                <span class="font-semibold text-gray-900 text-sm text-right">
                    <%= event.location %>
                </span>
            </div>
        <% } %>

        <!-- Organizer -->
        <% if (event.organizer) { %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-user-tie text-teal-500 mr-2"></i>
                    <span class="text-gray-600 text-sm">Organizer</span>
                </div>
                <span class="font-semibold text-gray-900 text-sm">
                    <%= event.organizer %>
                </span>
            </div>
        <% } %>

        <!-- Category -->
        <% if (event.category) { %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-tag text-purple-500 mr-2"></i>
                    <span class="text-gray-600 text-sm">Category</span>
                </div>
                <span class="font-semibold text-gray-900 text-sm">
                    <%= event.category %>
                </span>
            </div>
        <% } %>

        <!-- Capacity -->
        <% if (event.capacity) { %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-users text-orange-500 mr-2"></i>
                    <span class="text-gray-600 text-sm">Capacity</span>
                </div>
                <span class="font-semibold text-gray-900 text-sm">
                    <%= event.capacity %> people
                </span>
            </div>
        <% } %>

    </div>
</div>


                    <!-- Organizer Information -->
                    <% if (event.organizer) { %>
                        <div class="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl shadow-lg border border-indigo-100 p-6">
                            <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-user-tie mr-2 text-indigo-600"></i>
                                Event Organizer
                            </h3>
                            <div class="flex items-center p-4 bg-white rounded-lg shadow-sm border border-indigo-100">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center text-black font-bold text-lg mr-4">
                                    <%= event.organizer.charAt(0).toUpperCase() %>
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold text-gray-900"><%= event.organizer %></p>
                                    <p class="text-sm text-gray-500">Event Organizer</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <a href="/contact" class="btn btn-outline btn-sm w-full border-indigo-500 text-indigo-600 hover:bg-indigo-500 hover:text-black">
                                    <i class="fas fa-envelope mr-2"></i>
                                    Contact Organizer
                                </a>
                            </div>
                        </div>
                    <% } %>

                    <!-- Related Events Placeholder -->
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-calendar-week mr-2 text-orange-600"></i>
                            Related Events
                        </h3>
                        <div class="text-center py-8">
                            <i class="fas fa-calendar-alt text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500 text-sm mb-4">
                                Discover more upcoming events
                            </p>
                            <a href="/events" class="btn btn-outline btn-sm border-orange-500 text-orange-600 hover:bg-orange-500 hover:text-black">
                                <i class="fas fa-search mr-2"></i>
                                Browse Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Event Gallery Section -->
<% if (event.gallery && event.gallery.length > 0) { %>
<section class="py-16 sm:py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-images mr-3 text-blue-600"></i>
                Event Gallery
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Take a look at the moments captured during this event
            </p>
            <div class="mt-4 flex items-center justify-center text-sm text-gray-500">
                <i class="fas fa-camera mr-2"></i>
                <span><%= event.gallery.length %> image<%= event.gallery.length > 1 ? 's' : '' %></span>
            </div>
        </div>

        <!-- Gallery Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            <% event.gallery.forEach((image, index) => { %>
            <div class="gallery-item group cursor-pointer" onclick="openLightbox(<%= index %>)">
                <div class="aspect-square bg-gray-200 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <img src="<%= image %>"
                         alt="Event gallery image <%= index + 1 %>"
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                         loading="lazy">
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="bg-white bg-opacity-90 rounded-full p-3">
                                <i class="fas fa-expand-alt text-gray-800 text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <p class="text-sm text-gray-600">Image <%= index + 1 %></p>
                </div>
            </div>
            <% }); %>
        </div>

        <!-- View All Button (if more than 8 images) -->
        <% if (event.gallery.length > 8) { %>
        <div class="text-center mt-12">
            <button onclick="showAllImages()" class="btn btn-outline btn-lg border-blue-500 text-blue-600 hover:bg-blue-500 hover:text-white">
                <i class="fas fa-th mr-2"></i>
                View All <%= event.gallery.length %> Images
            </button>
        </div>
        <% } %>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <!-- Close Button -->
        <button onclick="closeLightbox()" class="absolute top-4 right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-all duration-200">
            <i class="fas fa-times text-xl"></i>
        </button>

        <!-- Navigation Buttons -->
        <button onclick="previousImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-3 transition-all duration-200">
            <i class="fas fa-chevron-left text-xl"></i>
        </button>

        <button onclick="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-3 transition-all duration-200">
            <i class="fas fa-chevron-right text-xl"></i>
        </button>

        <!-- Image Container -->
        <div class="relative">
            <img id="lightboxImage" src="" alt="Gallery image" class="max-w-full max-h-screen object-contain rounded-lg">

            <!-- Image Counter -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full">
                <span id="imageCounter">1 / 1</span>
            </div>
        </div>
    </div>
</div>
<% } %>

<!-- Enhanced Call to Action -->
<section class="py-16 sm:py-20 bg-gradient-to-br from-green-50 via-teal-50 to-blue-50 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-10 left-10 w-20 h-20 bg-green-500 rounded-full"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-teal-500 rounded-full"></div>
        <div class="absolute bottom-20 left-32 w-12 h-12 bg-blue-500 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-24 h-24 bg-indigo-500 rounded-full"></div>
    </div>

    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div class="bg-white rounded-2xl shadow-2xl p-8 sm:p-12 border border-gray-100">
            <div class="mb-6">
                <i class="fas fa-calendar-check text-5xl text-green-500 mb-4"></i>
            </div>

            <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">
                <% if (new Date(event.date) > new Date()) { %>
                    Don't Miss This Event!
                <% } else { %>
                    Thanks for Your Interest!
                <% } %>
            </h2>

            <p class="text-lg sm:text-xl text-gray-600 mb-8 sm:mb-10 max-w-3xl mx-auto leading-relaxed">
                <% if (new Date(event.date) > new Date()) { %>
                    Join us for this exciting event and be part of our growing community of science enthusiasts and researchers.
                <% } else { %>
                    This event has concluded. Stay tuned for more exciting events and opportunities to engage with our community.
                <% } %>
            </p>

            <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
                <% if (new Date(event.date) > new Date()) { %>
                    <button onclick="registerForEvent()" class="btn btn-primary btn-lg text-lg px-8 py-4 w-full sm:w-auto group">
                        <i class="fas fa-user-plus mr-2 group-hover:scale-110 transition-transform"></i>
                        Register Now
                    </button>


                <% } else { %>
                    <a href="/events" class="btn btn-primary btn-lg text-lg px-8 py-4 w-full sm:w-auto group">
                        <i class="fas fa-calendar-alt mr-2 group-hover:scale-110 transition-transform"></i>
                        View Upcoming Events
                    </a>
                <% } %>

                <button onclick="shareEvent()" class="btn btn-success btn-lg text-lg px-8 py-4 w-full sm:w-auto group">
                    <i class="fas fa-share-alt mr-2 group-hover:scale-110 transition-transform"></i>
                    Share Event
                </button>
            </div>


        </div>
    </div>
</section>

<!-- Toast Notification Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Set the event URL in the input field
    const eventUrlInput = document.getElementById('eventUrl');
    if (eventUrlInput) {
        eventUrlInput.value = window.location.href;
    }

    // Initialize countdown timer if event is upcoming
    const eventDate = new Date('<%= event.date %>');
    const now = new Date();

    if (eventDate > now) {
        initializeCountdown(eventDate);
    }
});

// Countdown Timer
function initializeCountdown(eventDate) {
    function updateCountdown() {
        const now = new Date().getTime();
        const eventTime = eventDate.getTime();
        const distance = eventTime - now;

        if (distance < 0) {
            // Event has started
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                countdownElement.innerHTML = '<div class="col-span-4 text-center text-lg font-bold">Event has started!</div>';
            }
            return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        const daysElement = document.getElementById('days');
        const hoursElement = document.getElementById('hours');
        const minutesElement = document.getElementById('minutes');
        const secondsElement = document.getElementById('seconds');

        if (daysElement) daysElement.textContent = days.toString().padStart(2, '0');
        if (hoursElement) hoursElement.textContent = hours.toString().padStart(2, '0');
        if (minutesElement) minutesElement.textContent = minutes.toString().padStart(2, '0');
        if (secondsElement) secondsElement.textContent = seconds.toString().padStart(2, '0');
    }

    // Update countdown immediately and then every second
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Toast notification function
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';

    toast.className = `${bgColor} text-black px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0 flex items-center space-x-3 max-w-sm`;
    toast.innerHTML = `
        <i class="fas ${icon} text-xl"></i>
        <span class="font-medium">${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-auto text-black hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.getElementById('toast-container').appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => toast.remove(), 300);
    }, 4000);
}

// Event Registration
function registerForEvent() {
    // Check if event is still upcoming
    const eventDate = new Date('<%= event.date %>');
    const now = new Date();

    if (eventDate <= now) {
        showToast('Registration for this event has ended', 'error');
        return;
    }

    // Simulate registration process
    showToast('Opening registration form...', 'info');

    // In a real application, this would open a registration modal or redirect to a registration page
    setTimeout(() => {
        showToast('Registration successful! Check your email for confirmation.', 'success');
    }, 2000);
}

// Add to Calendar
function addToCalendar() {
    const eventDate = new Date('<%= event.date %>');
    const title = encodeURIComponent('<%= event.title %>');
    const details = encodeURIComponent('<%= event.description %>');
    const location = encodeURIComponent('<%= event.location || "" %>');

    // Format date for Google Calendar (YYYYMMDDTHHMMSSZ)
    const startDate = eventDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    // Calculate end date (assume 2 hours if no end date provided)
    const endDate = new Date(eventDate.getTime() + (2 * 60 * 60 * 1000));
    const endDateFormatted = endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDate}/${endDateFormatted}&details=${details}&location=${location}`;

    window.open(googleCalendarUrl, '_blank');
    showToast('Opening calendar...', 'info');
}

// Maps and Directions
function openMaps() {
    const location = encodeURIComponent('<%= event.location || "" %>');
    if (!location) {
        showToast('No location specified for this event', 'error');
        return;
    }

    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${location}`;
    window.open(mapsUrl, '_blank');
    showToast('Opening maps...', 'info');
}

function getDirections() {
    const location = encodeURIComponent('<%= event.location || "" %>');
    if (!location) {
        showToast('No location specified for this event', 'error');
        return;
    }

    const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${location}`;
    window.open(directionsUrl, '_blank');
    showToast('Opening directions...', 'info');
}

// Enhanced share functions
function shareOnTwitter() {
    try {
        const url = encodeURIComponent(window.location.href);
        const eventDate = new Date('<%= event.date %>');
        const dateStr = eventDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
        const text = encodeURIComponent(`🎉 Join us for "<%= event.title %>" on ${dateStr}! <%= event.location ? 'at ' + event.location : '' %> #ScienceEvent #Community #Learning`);
        const shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;

        window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
        showToast('Opening Twitter share dialog...', 'info');
    } catch (error) {
        showToast('Failed to open Twitter share', 'error');
    }
}

function shareOnLinkedIn() {
    try {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('<%= event.title %>');
        const summary = encodeURIComponent('Join us for this exciting event - <%= event.description.substring(0, 100) %>...');
        const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`;

        window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
        showToast('Opening LinkedIn share dialog...', 'info');
    } catch (error) {
        showToast('Failed to open LinkedIn share', 'error');
    }
}

function shareOnFacebook() {
    try {
        const url = encodeURIComponent(window.location.href);
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;

        window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
        showToast('Opening Facebook share dialog...', 'info');
    } catch (error) {
        showToast('Failed to open Facebook share', 'error');
    }
}

function copyToClipboard() {
    const url = window.location.href;

    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(url).then(function() {
            showToast('Event link copied to clipboard!', 'success');
            updateCopyButton(true);
        }).catch(function(err) {
            fallbackCopyToClipboard(url);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(url);
    }
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showToast('Event link copied to clipboard!', 'success');
        updateCopyButton(true);
    } catch (err) {
        showToast('Failed to copy link. Please copy manually.', 'error');
    }

    document.body.removeChild(textArea);
}

function updateCopyButton(success) {
    const buttons = document.querySelectorAll('#copyButton, button[onclick="copyToClipboard()"]');
    buttons.forEach(button => {
        const originalHTML = button.innerHTML;

        if (success) {
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
            button.classList.add('bg-green-600', 'hover:bg-green-700');
            button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
        }

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-gray-600', 'hover:bg-gray-700');
        }, 2000);
    });
}

// Main share event function
function shareEvent() {
    if (navigator.share && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        // Use native share API on mobile devices
        const eventDate = new Date('<%= event.date %>');
        const dateStr = eventDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });

        navigator.share({
            title: '<%= event.title %>',
            text: `Join us for this event on ${dateStr}!`,
            url: window.location.href
        }).then(() => {
            showToast('Event shared successfully!', 'success');
        }).catch((error) => {
            // Fallback to copy to clipboard
            copyToClipboard();
        });
    } else {
        // Desktop: copy to clipboard
        copyToClipboard();
    }
}

// Gallery Lightbox Functions
<% if (event.gallery && event.gallery.length > 0) { %>
let currentImageIndex = 0;
const galleryImages = [
    <% event.gallery.forEach((image, index) => { %>
    '<%= image %>'<%= index < event.gallery.length - 1 ? ',' : '' %>
    <% }); %>
];

function openLightbox(index) {
    currentImageIndex = index;
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const imageCounter = document.getElementById('imageCounter');

    lightboxImage.src = galleryImages[currentImageIndex];
    imageCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;

    lightbox.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    updateLightboxImage();
}

function previousImage() {
    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    updateLightboxImage();
}

function updateLightboxImage() {
    const lightboxImage = document.getElementById('lightboxImage');
    const imageCounter = document.getElementById('imageCounter');

    lightboxImage.src = galleryImages[currentImageIndex];
    imageCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
}

function showAllImages() {
    // Scroll to gallery section
    const gallerySection = document.querySelector('.gallery-item').closest('section');
    gallerySection.scrollIntoView({ behavior: 'smooth' });

    showToast('Showing all gallery images', 'info');
}

// Close lightbox when clicking outside the image
document.getElementById('lightbox').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLightbox();
    }
});
<% } %>

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Gallery navigation (only when lightbox is open)
    <% if (event.gallery && event.gallery.length > 0) { %>
    const lightbox = document.getElementById('lightbox');
    if (!lightbox.classList.contains('hidden')) {
        if (e.key === 'ArrowLeft') {
            e.preventDefault();
            previousImage();
        } else if (e.key === 'ArrowRight') {
            e.preventDefault();
            nextImage();
        } else if (e.key === 'Escape') {
            e.preventDefault();
            closeLightbox();
        }
        return;
    }
    <% } %>

    // Ctrl+C or Cmd+C to copy link
    if ((e.ctrlKey || e.metaKey) && e.key === 'c' && !e.target.matches('input, textarea')) {
        e.preventDefault();
        copyToClipboard();
    }

    // Ctrl+S or Cmd+S to share
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        shareEvent();
    }

    // R key to register (if event is upcoming)
    if (e.key === 'r' && !e.target.matches('input, textarea')) {
        const eventDate = new Date('<%= event.date %>');
        const now = new Date();
        if (eventDate > now) {
            registerForEvent();
        }
    }
});
</script>
