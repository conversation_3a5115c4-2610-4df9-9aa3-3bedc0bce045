<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white shadow-sm rounded-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900">Welcome back, <%= user.name %>!</h1>
        <p class="mt-1 text-gray-600">Here's what's happening with your Science Club.</p>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow-sm rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/admin/projects/create" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                <i class="fas fa-flask text-blue-600 text-2xl"></i>
                <span class="ml-3 text-blue-900">Add New Project</span>
            </a>
            <a href="/admin/events/create" class="flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                <i class="fas fa-calendar text-yellow-600 text-2xl"></i>
                <span class="ml-3 text-yellow-900">Schedule Event</span>
            </a>
            <a href="/admin/resources/create" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                <i class="fas fa-book text-purple-600 text-2xl"></i>
                <span class="ml-3 text-purple-900">Add Resource</span>
            </a>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Projects Stats -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Projects</h3>
                <i class="fas fa-flask text-blue-600 text-xl"></i>
            </div>
            <p class="mt-2 text-3xl font-bold text-gray-900"><%= stats.projects %></p>
            <div class="mt-2 flex items-center text-sm">
                <span class="<%= percentageChanges.projects >= 0 ? 'text-green-600' : 'text-red-600' %>">
                    <i class="fas <%= percentageChanges.projects >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' %> mr-1"></i>
                    <%= Math.abs(percentageChanges.projects) %>%
                </span>
                <span class="text-gray-500 ml-2">from last month</span>
            </div>
        </div>

        <!-- Events Stats -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Events</h3>
                <i class="fas fa-calendar text-yellow-600 text-xl"></i>
            </div>
            <p class="mt-2 text-3xl font-bold text-gray-900"><%= stats.events %></p>
            <div class="mt-2 flex items-center text-sm">
                <span class="<%= percentageChanges.events >= 0 ? 'text-green-600' : 'text-red-600' %>">
                    <i class="fas <%= percentageChanges.events >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' %> mr-1"></i>
                    <%= Math.abs(percentageChanges.events) %>%
                </span>
                <span class="text-gray-500 ml-2">from last month</span>
            </div>
        </div>

        <!-- Resources Stats -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Resources</h3>
                <i class="fas fa-book text-purple-600 text-xl"></i>
            </div>
            <p class="mt-2 text-3xl font-bold text-gray-900"><%= stats.resources %></p>
            <div class="mt-2 flex items-center text-sm">
                <span class="<%= percentageChanges.resources >= 0 ? 'text-green-600' : 'text-red-600' %>">
                    <i class="fas <%= percentageChanges.resources >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' %> mr-1"></i>
                    <%= Math.abs(percentageChanges.resources) %>%
                </span>
                <span class="text-gray-500 ml-2">from last month</span>
            </div>
        </div>

        <!-- Users Stats -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Users</h3>
                <i class="fas fa-users text-indigo-600 text-xl"></i>
            </div>
            <p class="mt-2 text-3xl font-bold text-gray-900"><%= stats.users %></p>
            <div class="mt-2 flex items-center text-sm">
                <span class="<%= percentageChanges.users >= 0 ? 'text-green-600' : 'text-red-600' %>">
                    <i class="fas <%= percentageChanges.users >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' %> mr-1"></i>
                    <%= Math.abs(percentageChanges.users) %>%
                </span>
                <span class="text-gray-500 ml-2">from last month</span>
            </div>
        </div>

        <!-- Messages Stats -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Messages</h3>
                <i class="fas fa-envelope text-red-600 text-xl"></i>
            </div>
            <p class="mt-2 text-3xl font-bold text-gray-900"><%= stats.contacts %></p>
            <div class="mt-2 flex items-center text-sm">
                <span class="<%= percentageChanges.contacts >= 0 ? 'text-green-600' : 'text-red-600' %>">
                    <i class="fas <%= percentageChanges.contacts >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' %> mr-1"></i>
                    <%= Math.abs(percentageChanges.contacts) %>%
                </span>
                <span class="text-gray-500 ml-2">from last month</span>
            </div>
            <% if (stats.unreadContacts > 0) { %>
            <div class="mt-2 text-sm text-red-600">
                <%= stats.unreadContacts %> unread message<%= stats.unreadContacts > 1 ? 's' : '' %>
            </div>
            <% } %>
        </div>
    </div>

    <!-- Recent Activity and Notifications -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activity -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
            <div class="space-y-4">
                <% recentActivity.forEach(activity => { %>
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-8 w-8 rounded-full bg-<%= activity.color %>-100">
                            <i class="<%= activity.icon %> text-<%= activity.color %>-600"></i>
                        </span>
                    </div>
                    <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900"><%= activity.title %></p>
                        <p class="text-sm text-gray-500"><%= new Date(activity.time).toLocaleString() %></p>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>

        <!-- Notifications -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Notifications</h2>
            <div class="space-y-4" id="notifications-container">
                <% notifications.forEach(notification => { %>
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-8 w-8 rounded-full bg-<%= notification.color %>-100">
                            <i class="<%= notification.icon %> text-<%= notification.color %>-600"></i>
                        </span>
                    </div>
                    <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900"><%= notification.title %></p>
                        <p class="text-sm text-gray-500"><%= notification.description %></p>
                        <a href="<%= notification.link %>" class="text-sm text-primary-600 hover:text-primary-800">View details</a>
                    </div>
                </div>
                <% }); %>
                <% if (notifications.length === 0) { %>
                <p class="text-sm text-gray-500">No new notifications</p>
                <% } %>
            </div>
        </div>
    </div>

    <!-- Recent Items -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Projects -->
        <div class="bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900">Recent Projects</h2>
                <a href="/admin/projects" class="text-sm text-primary-600 hover:text-primary-800">View all</a>
            </div>
            <div class="space-y-4">
                <% recentProjects.forEach(project => { %>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900"><%= project.title %></h3>
                        <p class="text-sm text-gray-500"><%= new Date(project.createdAt).toLocaleDateString() %></p>
                    </div>
                    <a href="/admin/projects/<%= project._id %>" class="text-sm text-primary-600 hover:text-primary-800">View</a>
                </div>
                <% }); %>
            </div>
        </div>


    </div>
</div>

<!-- Real-time Updates Script -->
<script>
    // Function to update notifications
    async function updateNotifications() {
        try {
            const response = await fetch('/admin/api/notifications');
            const data = await response.json();

            const container = document.getElementById('notifications-container');
            if (data.notifications.length === 0) {
                container.innerHTML = '<p class="text-sm text-gray-500">No new notifications</p>';
                return;
            }

            container.innerHTML = data.notifications.map(notification => `
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-8 w-8 rounded-full bg-${notification.color}-100">
                            <i class="${notification.icon} text-${notification.color}-600"></i>
                        </span>
                    </div>
                    <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                        <p class="text-sm text-gray-500">${notification.description}</p>
                        <a href="${notification.link}" class="text-sm text-primary-600 hover:text-primary-800">View details</a>
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error updating notifications:', error);
        }
    }

    // Update notifications every minute
    setInterval(updateNotifications, 60000);
</script>
