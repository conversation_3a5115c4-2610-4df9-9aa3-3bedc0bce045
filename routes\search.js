const express = require('express');
const router = express.Router();
const apiClient = require('../utils/apiClient');

// Search results page
router.get('/', async (req, res) => {
  try {
    const { q: query, type, page = 1 } = req.query;
    const limit = 12;
    const skip = (page - 1) * limit;

    if (!query) {
      return res.render('pages/search/index', {
        title: 'Search',
        currentPage: 'search',
        query: '',
        results: [],
        total: 0,
        currentPageNum: 1,
        totalPages: 0,
        searchTypes: ['all', 'projects', 'resources', 'events']
      });
    }

    // Build search parameters
    const { category, author, dateFrom, dateTo, featured, sortBy, sortOrder } = req.query;
    const searchParams = {
      type: type || 'all',
      page: parseInt(page),
      limit: limit
    };

    // Add optional filters
    if (category) searchParams.category = category;
    if (author) searchParams.author = author;
    if (dateFrom) searchParams.dateFrom = dateFrom;
    if (dateTo) searchParams.dateTo = dateTo;
    if (featured) searchParams.featured = featured;
    if (sortBy) searchParams.sortBy = sortBy;
    if (sortOrder) searchParams.sortOrder = sortOrder;

    // Make API call to search
    const searchResponse = await apiClient.search(query, searchParams);

    let results = [];
    let total = 0;
    let totalPages = 0;

    if (searchResponse.success) {
      results = searchResponse.data.results || [];
      total = searchResponse.data.total || 0;
      totalPages = Math.ceil(total / limit);
    } else {
      console.error('Search API error:', searchResponse.message);
      // Fall back to empty results with error message
    }

    res.render('pages/search/index', {
      title: `Search Results for "${query}"`,
      currentPage: 'search',
      query: query,
      results: results,
      total: total,
      currentPageNum: parseInt(page),
      totalPages: totalPages,
      searchTypes: ['all', 'projects', 'resources', 'events'],
      selectedType: type || 'all',
      error: searchResponse.success ? null : 'Search temporarily unavailable. Please try again later.'
    });

  } catch (error) {
    console.error('Search page error:', error);
    res.status(500).render('pages/error', {
      title: 'Search Error',
      error: error
    });
  }
});

// Advanced search page
router.get('/advanced', (req, res) => {
  res.render('pages/search/advanced', {
    title: 'Advanced Search',
    currentPage: 'search'
  });
});

module.exports = router;
