const apiClient = require('../utils/apiClient');
const { validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const Project = require('../models/Project');

// Helper function to get auth token from session
const getAuthToken = (req) => {
  return req.session.authToken || req.session.user?.token;
};

// Get all public projects
exports.getAllProjects = async (req, res) => {
  try {
    const response = await apiClient.getProjects();

    if (!response.success) {
      throw new Error(response.message);
    }

    const projects = response.data.projects || response.data || [];

    res.render('pages/projects/index', {
      title: 'Our Projects',
      currentPage: 'projects',
      projects
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching projects'
    });
  }
};

// Get single public project
exports.getProjectById = async (req, res) => {
  try {
    const response = await apiClient.getProject(req.params.id);

    if (!response.success) {
      if (response.status === 404) {
        return res.status(404).render('pages/404', {
          title: '404 - Project Not Found'
        });
      }
      throw new Error(response.message);
    }

    const project = response.data;

    res.render('pages/projects/show', {
      title: project.title,
      currentPage: 'projects',
      project
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching project'
    });
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/projects');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    if (extname) {
      return cb(null, true);
    }
    cb(new Error('Invalid file type!'));
  }
}).fields([
  { name: 'image', maxCount: 1 },
  { name: 'pdfFile', maxCount: 1 }
]);

// Get all projects for admin dashboard
exports.getAdminProjects = async (req, res) => {
  try {
    const token = getAuthToken(req);
    const response = await apiClient.getProjects({ admin: true });

    if (!response.success) {
      throw new Error(response.message);
    }

    const projects = response.data.projects || response.data || [];

    res.render('admin/projects/index', {
      projects,
      title: 'Manage Projects',
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching admin projects:', error);
    req.flash('error', 'Error fetching projects');
    res.redirect('/admin/dashboard');
  }
};

// Show create project form
exports.getCreateProjectForm = (req, res) => {
  res.render('admin/projects/create', {
    title: 'Create New Project',
    user: req.session.user
  });
};

// Create new project
exports.createProject = async (req, res) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        req.flash('error', err.message);
        return res.redirect('/admin/projects/create');
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        req.flash('error', errors.array()[0].msg);
        return res.redirect('/admin/projects/create');
      }

      // Handle file uploads
      const image = req.files && req.files.image && req.files.image[0]
        ? `/uploads/projects/${req.files.image[0].filename}`
        : null;

      const pdfFile = req.files && req.files.pdfFile && req.files.pdfFile[0]
        ? `/uploads/projects/${req.files.pdfFile[0].filename}`
        : null;

      // Parse authors and tags
      const authors = req.body.authors ? req.body.authors.split(',').map(author => author.trim()) : [];
      const tags = req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [];

      const project = new Project({
        title: req.body.title,
        description: req.body.description,
        image: image,
        pdfFile: pdfFile,
        authors: authors,
        tags: tags,
        featured: req.body.featured === 'true'
      });

      await project.save();
      req.flash('success', 'Project created successfully');
      res.redirect('/admin/projects');
    });
  } catch (error) {
    req.flash('error', 'Error creating project');
    res.redirect('/admin/projects/create');
  }
};

// Show edit project form
exports.getEditProjectForm = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    if (!project) {
      req.flash('error', 'Project not found');
      return res.redirect('/admin/projects');
    }

    res.render('admin/projects/edit', {
      project,
      title: 'Edit Project',
      user: req.session.user
    });
  } catch (error) {
    req.flash('error', 'Error fetching project');
    res.redirect('/admin/projects');
  }
};

// Update project
exports.updateProject = async (req, res) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        req.flash('error', err.message);
        return res.redirect(`/admin/projects/${req.params.id}/edit`);
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        req.flash('error', errors.array()[0].msg);
        return res.redirect(`/admin/projects/${req.params.id}/edit`);
      }

      const project = await Project.findById(req.params.id);
      if (!project) {
        req.flash('error', 'Project not found');
        return res.redirect('/admin/projects');
      }

      // Handle file uploads
      const image = req.files && req.files.image && req.files.image[0]
        ? `/uploads/projects/${req.files.image[0].filename}`
        : project.image;

      const pdfFile = req.files && req.files.pdfFile && req.files.pdfFile[0]
        ? `/uploads/projects/${req.files.pdfFile[0].filename}`
        : project.pdfFile;

      // Parse authors and tags
      const authors = req.body.authors ? req.body.authors.split(',').map(author => author.trim()) : project.authors;
      const tags = req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : project.tags;

      project.title = req.body.title;
      project.description = req.body.description;
      project.image = image;
      project.pdfFile = pdfFile;
      project.authors = authors;
      project.tags = tags;
      project.featured = req.body.featured === 'true';

      await project.save();
      req.flash('success', 'Project updated successfully');
      res.redirect('/admin/projects');
    });
  } catch (error) {
    req.flash('error', 'Error updating project');
    res.redirect(`/admin/projects/${req.params.id}/edit`);
  }
};

// Delete project
exports.deleteProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    if (!project) {
      if (req.xhr || req.headers.accept.indexOf('json') > -1) {
        return res.status(404).json({ success: false, error: 'Project not found' });
      }
      req.flash('error', 'Project not found');
      return res.redirect('/admin/projects');
    }

    await project.deleteOne(); // Using deleteOne instead of remove which is deprecated

    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.json({ success: true, message: 'Project deleted successfully' });
    }

    req.flash('success', 'Project deleted successfully');
    res.redirect('/admin/projects');
  } catch (error) {
    console.error('Delete project error:', error);
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(500).json({ success: false, error: 'Error deleting project' });
    }
    req.flash('error', 'Error deleting project');
    res.redirect('/admin/projects');
  }
};

// Toggle project featured status
exports.toggleProjectFeatured = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    project.featured = !project.featured;
    await project.save();

    res.json({
      message: `Project ${project.featured ? 'featured' : 'unfeatured'} successfully`,
      featured: project.featured
    });
  } catch (error) {
    res.status(500).json({ error: 'Error toggling project featured status' });
  }
};

// Bulk action on projects
exports.bulkActionProjects = async (req, res) => {
  try {
    const { action, ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, error: 'No projects selected' });
    }

    switch (action) {
      case 'delete':
        await Project.deleteMany({ _id: { $in: ids } });
        return res.json({ success: true, message: 'Projects deleted successfully' });

      case 'toggle-featured':
        // Get current featured status of projects
        const projects = await Project.find({ _id: { $in: ids } });
        const allFeatured = projects.every(p => p.featured);

        // Toggle featured status based on current state
        await Project.updateMany(
          { _id: { $in: ids } },
          { $set: { featured: !allFeatured } }
        );
        return res.json({
          success: true,
          message: `Projects ${!allFeatured ? 'featured' : 'unfeatured'} successfully`
        });

      default:
        return res.status(400).json({ success: false, error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Bulk action error:', error);
    return res.status(500).json({ success: false, error: 'Error processing bulk action' });
  }
};
