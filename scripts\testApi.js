const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;

// Helper function to make API requests
async function apiRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (useAuth && authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response) {
      return error.response.data;
    }
    throw error;
  }
}

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing API Health Check...');
  const result = await apiRequest('GET', '/health');
  console.log('Health Check Result:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.data) {
    console.log('API Status:', result.data.status);
    console.log('Environment:', result.data.environment);
  }
}

async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');

  // Test login
  const loginResult = await apiRequest('POST', '/auth/login', TEST_USER);

  if (loginResult.success && loginResult.data.token) {
    authToken = loginResult.data.token;
    console.log('Login:', '✅ PASS');
    console.log('User:', loginResult.data.user.name, `(${loginResult.data.user.role})`);

    // Test token verification
    const verifyResult = await apiRequest('POST', '/auth/verify', null, true);
    console.log('Token Verification:', verifyResult.success ? '✅ PASS' : '❌ FAIL');

    // Test get current user
    const meResult = await apiRequest('GET', '/auth/me', null, true);
    console.log('Get Current User:', meResult.success ? '✅ PASS' : '❌ FAIL');

  } else {
    console.log('Login:', '❌ FAIL');
    console.log('Error:', loginResult.message);
  }
}

async function testProjects() {
  console.log('\n📚 Testing Projects API...');

  // Test get all projects
  const projectsResult = await apiRequest('GET', '/projects');
  console.log('Get Projects:', projectsResult.success ? '✅ PASS' : '❌ FAIL');
  console.log('Projects Count:', projectsResult.data ? projectsResult.data.length : 0);

  // Test get featured projects
  const featuredResult = await apiRequest('GET', '/projects/featured');
  console.log('Get Featured Projects:', featuredResult.success ? '✅ PASS' : '❌ FAIL');

  // Test create project (requires auth)
  if (authToken) {
    const newProject = {
      title: 'Test API Project',
      description: 'This is a test project created via API',
      authors: ['API Tester'],
      tags: ['test', 'api'],
      featured: false
    };

    const createResult = await apiRequest('POST', '/projects', newProject, true);
    console.log('Create Project:', createResult.success ? '✅ PASS' : '❌ FAIL');

    if (createResult.success && createResult.data) {
      const projectId = createResult.data._id;

      // Test get project by ID
      const getResult = await apiRequest('GET', `/projects/${projectId}`);
      console.log('Get Project by ID:', getResult.success ? '✅ PASS' : '❌ FAIL');

      // Test update project
      const updateData = { title: 'Updated Test API Project' };
      const updateResult = await apiRequest('PUT', `/projects/${projectId}`, updateData, true);
      console.log('Update Project:', updateResult.success ? '✅ PASS' : '❌ FAIL');

      // Test delete project
      const deleteResult = await apiRequest('DELETE', `/projects/${projectId}`, null, true);
      console.log('Delete Project:', deleteResult.success ? '✅ PASS' : '❌ FAIL');
    }
  }
}

async function testContacts() {
  console.log('\n📧 Testing Contacts API...');

  // Test submit contact form
  const contactData = {
    name: 'API Test User',
    email: '<EMAIL>',
    phone: '+1234567890',
    subject: 'API Test Contact',
    message: 'This is a test contact submission via API'
  };

  const submitResult = await apiRequest('POST', '/contacts', contactData);
  console.log('Submit Contact Form:', submitResult.success ? '✅ PASS' : '❌ FAIL');

  // Test get contacts (admin only)
  if (authToken) {
    const contactsResult = await apiRequest('GET', '/contacts', null, true);
    console.log('Get Contacts (Admin):', contactsResult.success ? '✅ PASS' : '❌ FAIL');

    if (contactsResult.success && contactsResult.data && contactsResult.data.length > 0) {
      const contactId = contactsResult.data[0]._id;

      // Test mark as read
      const readResult = await apiRequest('PATCH', `/contacts/${contactId}/read`, { isRead: true }, true);
      console.log('Mark Contact as Read:', readResult.success ? '✅ PASS' : '❌ FAIL');
    }
  }
}

async function testSearch() {
  console.log('\n🔍 Testing Search API...');

  // Test search
  const searchResult = await apiRequest('GET', '/search?q=test&limit=5');
  console.log('Search:', searchResult.success ? '✅ PASS' : '❌ FAIL');

  // Test search suggestions
  const suggestionsResult = await apiRequest('GET', '/search/suggestions?q=test');
  console.log('Search Suggestions:', suggestionsResult.success ? '✅ PASS' : '❌ FAIL');

  // Test popular search terms
  const popularResult = await apiRequest('GET', '/search/popular');
  console.log('Popular Search Terms:', popularResult.success ? '✅ PASS' : '❌ FAIL');
}

async function testDocumentation() {
  console.log('\n📖 Testing API Documentation...');

  const docsResult = await apiRequest('GET', '/docs');
  console.log('API Documentation:', docsResult.success ? '✅ PASS' : '❌ FAIL');
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting API Tests...');
  console.log('='.repeat(50));

  try {
    await testHealthCheck();
    await testAuthentication();
    await testProjects();
    await testContacts();
    await testSearch();
    await testDocumentation();

    console.log('\n' + '='.repeat(50));
    console.log('✅ API Tests Completed!');
    console.log('\nTo test the API manually:');
    console.log('1. Start the server: npm run dev');
    console.log('2. Visit: http://localhost:3000/api/docs');
    console.log('3. Use Postman or curl to test endpoints');

  } catch (error) {
    console.error('\n❌ Test Error:', error.message);
    console.log('\nMake sure the server is running: npm run dev');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, apiRequest };
