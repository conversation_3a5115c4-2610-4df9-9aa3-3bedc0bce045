/**
 * Frontend API Client for Science Club
 * Handles all API calls from the browser
 */

class FrontendApiClient {
  constructor() {
    this.baseURL = window.location.origin;
    this.apiPrefix = '/api';
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${this.apiPrefix}${endpoint}`;

    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message || 'Success'
      };
    } catch (error) {
      console.error('API Request Error:', error);
      return {
        success: false,
        data: null,
        message: error.message || 'An error occurred'
      };
    }
  }

  // Projects API
  async getProjects(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/projects${queryString ? `?${queryString}` : ''}`;
    return this.makeRequest(endpoint);
  }

  async getProject(id) {
    return this.makeRequest(`/projects/${id}`);
  }

  async getFeaturedProjects() {
    return this.makeRequest('/projects?featured=true');
  }



  // Events API
  async getEvents(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/events${queryString ? `?${queryString}` : ''}`;
    return this.makeRequest(endpoint);
  }

  async getEvent(id) {
    return this.makeRequest(`/events/${id}`);
  }

  async getUpcomingEvents() {
    return this.makeRequest('/events?upcoming=true');
  }

  // Resources API
  async getResources(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/resources${queryString ? `?${queryString}` : ''}`;
    return this.makeRequest(endpoint);
  }

  async getResource(id) {
    return this.makeRequest(`/resources/${id}`);
  }

  async getResourcesByCategory(category) {
    return this.makeRequest(`/resources?category=${encodeURIComponent(category)}`);
  }

  // Contact API
  async submitContact(data) {
    return this.makeRequest('/contacts', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // Search API
  async search(query, params = {}) {
    const searchParams = { q: query, ...params };
    const queryString = new URLSearchParams(searchParams).toString();
    return this.makeRequest(`/search?${queryString}`);
  }

  // Auth API (for admin functions)
  async login(credentials) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });
  }

  // Admin API calls (require authentication)
  async makeAuthenticatedRequest(endpoint, options = {}) {
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

    if (!token) {
      return {
        success: false,
        data: null,
        message: 'Authentication required'
      };
    }

    const authOptions = {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      }
    };

    return this.makeRequest(endpoint, authOptions);
  }

  // Admin Projects
  async createProject(data) {
    return this.makeAuthenticatedRequest('/projects', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async updateProject(id, data) {
    return this.makeAuthenticatedRequest(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteProject(id) {
    return this.makeAuthenticatedRequest(`/projects/${id}`, {
      method: 'DELETE'
    });
  }



  // Admin Events
  async createEvent(data) {
    return this.makeAuthenticatedRequest('/events', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async updateEvent(id, data) {
    return this.makeAuthenticatedRequest(`/events/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteEvent(id) {
    return this.makeAuthenticatedRequest(`/events/${id}`, {
      method: 'DELETE'
    });
  }

  // Admin Resources
  async createResource(data) {
    return this.makeAuthenticatedRequest('/resources', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async updateResource(id, data) {
    return this.makeAuthenticatedRequest(`/resources/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteResource(id) {
    return this.makeAuthenticatedRequest(`/resources/${id}`, {
      method: 'DELETE'
    });
  }

  // Admin Contacts
  async getContacts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/contacts${queryString ? `?${queryString}` : ''}`;
    return this.makeAuthenticatedRequest(endpoint);
  }

  async getContact(id) {
    return this.makeAuthenticatedRequest(`/contacts/${id}`);
  }
}

// Create global instance
window.apiClient = new FrontendApiClient();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FrontendApiClient;
}
