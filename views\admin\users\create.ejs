<!-- Admin Create User -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-user-plus mr-2 text-indigo-600"></i>Create New User
        </h1>
        <a href="/admin/users" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to Users
        </a>
    </div>
</div>

<!-- Error Message -->
<% if (typeof error !== 'undefined' && error) { %>
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
            <span class="text-red-800"><%= error %></span>
        </div>
    </div>
<% } %>

<!-- Success Message -->
<% if (typeof success !== 'undefined' && success) { %>
    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <i class="fas fa-check-circle text-green-600 mr-2"></i>
            <span class="text-green-800"><%= success %></span>
        </div>
    </div>
<% } %>

<!-- Create User Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form action="/admin/users" method="POST" id="createUserForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name -->
            <div class="form-group">
                <label for="name" class="form-label">
                    Full Name <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    class="form-input"
                    value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>"
                    required
                    placeholder="Enter full name"
                >
            </div>

            <!-- Email -->
            <div class="form-group">
                <label for="email" class="form-label">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    value="<%= typeof formData !== 'undefined' ? formData.email || '' : '' %>"
                    required
                    placeholder="Enter email address"
                >
            </div>

            <!-- Password -->
            <div class="form-group">
                <label for="password" class="form-label">
                    Password <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input pr-10"
                        required
                        placeholder="Enter password"
                        minlength="6"
                    >
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onclick="togglePassword('password')"
                    >
                        <i class="fas fa-eye text-gray-400" id="password-icon"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mt-1">Minimum 6 characters</p>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
                <label for="confirmPassword" class="form-label">
                    Confirm Password <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        class="form-input pr-10"
                        required
                        placeholder="Confirm password"
                        minlength="6"
                    >
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onclick="togglePassword('confirmPassword')"
                    >
                        <i class="fas fa-eye text-gray-400" id="confirmPassword-icon"></i>
                    </button>
                </div>
            </div>

            <!-- Role -->
            <div class="form-group">
                <label for="role" class="form-label">
                    Role <span class="text-red-500">*</span>
                </label>
                <select id="role" name="role" class="form-input" required>
                    <option value="">Select Role</option>
                    <option value="admin" <%= typeof formData !== 'undefined' && formData.role === 'admin' ? 'selected' : '' %>>
                        Administrator
                    </option>
                    <option value="editor" <%= typeof formData !== 'undefined' && formData.role === 'editor' ? 'selected' : '' %>>
                        Editor
                    </option>
                </select>
                <p class="text-sm text-gray-500 mt-1">
                    <strong>Administrator:</strong> Full access to all features<br>
                    <strong>Editor:</strong> Can manage content but not users or settings
                </p>
            </div>

            <!-- Status -->
            <div class="form-group">
                <label for="status" class="form-label">
                    Account Status
                </label>
                <select id="status" name="status" class="form-input">
                    <option value="active" <%= typeof formData !== 'undefined' && formData.status === 'active' ? 'selected' : 'selected' %>>
                        Active
                    </option>
                    <option value="inactive" <%= typeof formData !== 'undefined' && formData.status === 'inactive' ? 'selected' : '' %>>
                        Inactive
                    </option>
                </select>
            </div>
        </div>

        <!-- Bio/Notes -->
        <div class="form-group mt-6">
            <label for="bio" class="form-label">
                Bio/Notes (Optional)
            </label>
            <textarea
                id="bio"
                name="bio"
                rows="4"
                class="form-input form-textarea"
                placeholder="Enter any additional notes about this user..."
            ><%= typeof formData !== 'undefined' ? formData.bio || '' : '' %></textarea>
        </div>

        <!-- Permissions Section -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-shield-alt mr-2 text-indigo-600"></i>Permissions
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="manage_projects" class="form-checkbox mr-2">
                        <span class="text-sm text-gray-700">Manage Projects</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="manage_blog" class="form-checkbox mr-2">
                        <span class="text-sm text-gray-700">Manage Blog Posts</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="manage_resources" class="form-checkbox mr-2">
                        <span class="text-sm text-gray-700">Manage Resources</span>
                    </label>
                </div>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="manage_events" class="form-checkbox mr-2">
                        <span class="text-sm text-gray-700">Manage Events</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="view_messages" class="form-checkbox mr-2">
                        <span class="text-sm text-gray-700">View Contact Messages</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="permissions[]" value="manage_users" class="form-checkbox mr-2" id="manage-users">
                        <span class="text-sm text-gray-700">Manage Users</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" name="sendWelcomeEmail" value="true" class="form-checkbox mr-2" checked>
                    <span class="text-sm text-gray-700">Send welcome email to user</span>
                </label>
            </div>
            <div class="flex space-x-4">
                <a href="/admin/users" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-user-plus mr-2"></i>Create User
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createUserForm');
    const roleSelect = document.getElementById('role');
    const manageUsersCheckbox = document.getElementById('manage-users');
    const submitBtn = document.getElementById('submitBtn');

    // Role-based permission management
    roleSelect.addEventListener('change', function() {
        const role = this.value;
        const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
        
        if (role === 'admin') {
            checkboxes.forEach(cb => cb.checked = true);
        } else if (role === 'editor') {
            checkboxes.forEach(cb => {
                if (cb.value === 'manage_users') {
                    cb.checked = false;
                } else {
                    cb.checked = true;
                }
            });
        } else {
            checkboxes.forEach(cb => cb.checked = false);
        }
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match!');
            return false;
        }

        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long!');
            return false;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating User...';
    });

    // Password strength indicator
    const passwordInput = document.getElementById('password');
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        // Could add visual password strength indicator here
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    return strength;
}
</script>
