const express = require('express');
const router = express.Router();
const Resource = require('../../models/Resource');
const ApiResponse = require('../../utils/apiResponse');
const { verifyToken, requireEditor } = require('../../middleware/apiAuth');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

/**
 * @route   GET /api/resources
 * @desc    Get all resources with pagination
 * @access  Public
 */
router.get('/',
  validationChains.getPaginated,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query
      const query = {};

      // Filter by category if specified
      if (req.query.category) {
        query.category = req.query.category;
      }

      // Filter by resource type if specified
      if (req.query.type) {
        query.type = req.query.type;
      }

      // Filter by tags if specified
      if (req.query.tags) {
        const tags = Array.isArray(req.query.tags) ? req.query.tags : [req.query.tags];
        query.tags = { $in: tags };
      }

      // Search by title or description
      if (req.query.search) {
        query.$or = [
          { title: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const resources = await Resource.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Resource.countDocuments(query);
      const totalPages = Math.ceil(total / limit);

      const pagination = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      ApiResponse.paginated(res, resources, pagination, 'Resources retrieved successfully');

    } catch (error) {
      console.error('Get resources error:', error);
      ApiResponse.error(res, 'Failed to retrieve resources');
    }
  }
);

/**
 * @route   GET /api/resources/category/:category
 * @desc    Get resources by category
 * @access  Public
 */
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const limit = parseInt(req.query.limit) || 20;

    const resources = await Resource.find({ category })
      .sort({ createdAt: -1 })
      .limit(limit);

    ApiResponse.success(res, resources, `Resources in category "${category}" retrieved successfully`);

  } catch (error) {
    console.error('Get resources by category error:', error);
    ApiResponse.error(res, 'Failed to retrieve resources by category');
  }
});

/**
 * @route   GET /api/resources/categories/all
 * @desc    Get all unique categories
 * @access  Public
 */
router.get('/categories/all', async (req, res) => {
  try {
    const categories = await Resource.distinct('category');
    ApiResponse.success(res, categories, 'Categories retrieved successfully');
  } catch (error) {
    console.error('Get categories error:', error);
    ApiResponse.error(res, 'Failed to retrieve categories');
  }
});

/**
 * @route   GET /api/resources/:id
 * @desc    Get resource by ID
 * @access  Public
 */
router.get('/:id',
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const resource = await Resource.findById(req.params.id);

      if (!resource) {
        return ApiResponse.notFound(res, 'Resource not found');
      }

      ApiResponse.success(res, resource, 'Resource retrieved successfully');

    } catch (error) {
      console.error('Get resource error:', error);
      ApiResponse.error(res, 'Failed to retrieve resource');
    }
  }
);

/**
 * @route   POST /api/resources
 * @desc    Create new resource
 * @access  Private (Editor/Admin)
 */
router.post('/',
  verifyToken,
  requireEditor,
  validationChains.createResource,
  handleValidationErrors,
  async (req, res) => {
    try {
      const resourceData = {
        title: req.body.title,
        description: req.body.description,
        category: req.body.category,
        type: req.body.type || 'link',
        url: req.body.url,
        tags: req.body.tags || []
      };

      // Handle file path for uploaded files
      if (req.body.filePath) {
        resourceData.filePath = req.body.filePath;
      }

      const resource = new Resource(resourceData);
      await resource.save();

      ApiResponse.success(res, resource, 'Resource created successfully', 201);

    } catch (error) {
      console.error('Create resource error:', error);
      ApiResponse.error(res, 'Failed to create resource');
    }
  }
);

/**
 * @route   PUT /api/resources/:id
 * @desc    Update resource
 * @access  Private (Editor/Admin)
 */
router.put('/:id',
  verifyToken,
  requireEditor,
  validationChains.updateResource,
  handleValidationErrors,
  async (req, res) => {
    try {
      const resource = await Resource.findById(req.params.id);

      if (!resource) {
        return ApiResponse.notFound(res, 'Resource not found');
      }

      // Update fields
      const allowedUpdates = ['title', 'description', 'category', 'type', 'url', 'tags', 'filePath'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          resource[field] = req.body[field];
        }
      });

      await resource.save();

      ApiResponse.success(res, resource, 'Resource updated successfully');

    } catch (error) {
      console.error('Update resource error:', error);
      ApiResponse.error(res, 'Failed to update resource');
    }
  }
);

/**
 * @route   DELETE /api/resources/:id
 * @desc    Delete resource
 * @access  Private (Editor/Admin)
 */
router.delete('/:id',
  verifyToken,
  requireEditor,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const resource = await Resource.findById(req.params.id);

      if (!resource) {
        return ApiResponse.notFound(res, 'Resource not found');
      }

      await Resource.findByIdAndDelete(req.params.id);

      ApiResponse.success(res, null, 'Resource deleted successfully');

    } catch (error) {
      console.error('Delete resource error:', error);
      ApiResponse.error(res, 'Failed to delete resource');
    }
  }
);

module.exports = router;
