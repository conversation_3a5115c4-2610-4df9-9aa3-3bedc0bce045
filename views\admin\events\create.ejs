<!-- Create Event Form -->
<div class="admin-header">
    <h1 class="admin-title">
        <i class="fas fa-calendar-plus mr-3"></i>Create New Event
    </h1>
    <p class="text-slate-200 mt-2">Organize and promote exciting science events for the community</p>
</div>

<div class="form-container">
    <form action="/admin/events/create" method="POST" enctype="multipart/form-data">
        <!-- Event Details Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-info-circle"></i>Event Information
            </h3>

            <div class="form-group">
                <label for="title" class="form-label required">Event Title</label>
                <input type="text" id="title" name="title" class="form-input" required
                       placeholder="Enter an exciting event title">
                <p class="form-help">Create a compelling title that attracts participants</p>
            </div>

            <div class="form-group">
                <label for="description" class="form-label required">Event Description</label>
                <textarea id="description" name="description" rows="6" class="form-textarea" required
                          placeholder="Describe your event in detail. Include what participants will learn, activities planned, requirements, and what makes this event special..."></textarea>
                <p class="form-help">Provide comprehensive details about the event, activities, and expectations</p>
            </div>

            <div class="form-group">
                <label for="location" class="form-label required">Event Location</label>
                <input type="text" id="location" name="location" class="form-input" required
                       placeholder="Science Lab Room 101, Online via Zoom, or Community Center">
                <p class="form-help">Specify the venue address or online meeting details</p>
            </div>
        </div>

        <!-- Schedule Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-clock"></i>Event Schedule
            </h3>

            <div class="form-grid">
                <div class="form-group">
                    <label for="startDate" class="form-label required">Start Date & Time</label>
                    <input type="datetime-local" id="startDate" name="startDate" class="form-input" required>
                    <p class="form-help">When does the event begin?</p>
                </div>
                <div class="form-group">
                    <label for="endDate" class="form-label">End Date & Time</label>
                    <input type="datetime-local" id="endDate" name="endDate" class="form-input">
                    <p class="form-help">When does the event end? (optional)</p>
                </div>
            </div>
        </div>

        <!-- Registration Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-user-plus"></i>Registration Details
            </h3>

            <div class="form-group">
                <label for="registrationLink" class="form-label">Registration Link</label>
                <input type="url" id="registrationLink" name="registrationLink" class="form-input"
                       placeholder="https://forms.google.com/... or https://eventbrite.com/...">
                <p class="form-help">Provide a link where participants can register for the event</p>
            </div>
        </div>

        <!-- Media Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-image"></i>Event Image
            </h3>

            <!-- Cover Image Upload -->
            <div class="form-group">
                <label for="image" class="form-label">
                    <i class="fas fa-image mr-2"></i>Upload Cover Image
                </label>
                <input type="file" id="image" name="image" accept="image/*" class="form-input">
                <p class="form-help">Upload a cover image for the event (JPG, PNG, GIF up to 5MB)</p>
            </div>

            <!-- Gallery Images Upload -->
            <div class="form-group">
                <label for="galleryImages" class="form-label">
                    <i class="fas fa-images mr-2"></i>Upload Gallery Images (Optional)
                </label>
                <input type="file" id="galleryImages" name="galleryImages" multiple accept="image/*" class="form-input">
                <p class="form-help">Upload multiple images for the event gallery (JPG, PNG, GIF up to 5MB each, max 10 images)</p>
            </div>
        </div>

        <!-- Event Settings Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-cog"></i>Event Settings
            </h3>

            <div class="checkbox-wrapper">
                <input type="checkbox" id="featured" name="featured" value="true">
                <label for="featured" class="font-medium text-gray-700">
                    <span class="text-lg">⭐</span> Mark as Featured Event
                    <p class="text-sm text-gray-500 mt-1">Featured events appear prominently on the events page</p>
                </label>
            </div>


        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/admin/events" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>Create Event
            </button>
        </div>
    </form>
</div>

<!-- Simple Events Form Script -->
<script>

    // Date validation
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');

    startDateInput.addEventListener('change', function() {
        const startDate = new Date(this.value);
        const now = new Date();

        if (startDate < now) {
            alert('Warning: You are scheduling an event in the past.');
        }

        // Set minimum end date to start date
        if (this.value) {
            endDateInput.min = this.value;
        }
    });

    endDateInput.addEventListener('change', function() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(this.value);

        if (endDate <= startDate) {
            alert('End date must be after start date.');
            this.value = '';
        }
    });

    // Auto-resize description textarea
    const descriptionTextarea = document.getElementById('description');
    descriptionTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Form validation with better UX
    document.querySelector('form').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const location = document.getElementById('location').value.trim();
        const startDate = document.getElementById('startDate').value;

        const errors = [];
        if (!title) errors.push('Event Title');
        if (!description) errors.push('Event Description');
        if (!location) errors.push('Event Location');
        if (!startDate) errors.push('Start Date & Time');

        if (errors.length > 0) {
            e.preventDefault();
            alert(`Please fill in the following required fields:\n• ${errors.join('\n• ')}`);

            // Focus on first empty field
            if (!title) document.getElementById('title').focus();
            else if (!description) document.getElementById('description').focus();
            else if (!location) document.getElementById('location').focus();
            else if (!startDate) document.getElementById('startDate').focus();
        }
    });

    // Set default start date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0); // Default to 10 AM
    startDateInput.value = tomorrow.toISOString().slice(0, 16);
</script>
