<!-- Edit Event Form -->
<div class="admin-header">
    <h1 class="admin-title">
        <i class="fas fa-edit mr-2 text-yellow-600"></i>Edit Event
    </h1>
</div>

<div class="bg-white rounded-lg shadow-md p-6">
    <form id="editEventForm" action="/admin/events/<%= event._id %>" method="POST" enctype="multipart/form-data" class="space-y-6">
        <input type="hidden" name="_method" value="PUT">

        <!-- Title -->
        <div class="form-group">
            <label for="title" class="form-label required">Event Title</label>
            <input type="text" id="title" name="title" class="form-input" value="<%= event.title %>" required>
        </div>

        <!-- Description -->
        <div class="form-group">
            <label for="description" class="form-label required">Description</label>
            <textarea id="description" name="description" rows="5" class="form-textarea" required><%= event.description %></textarea>
        </div>

        <!-- Location -->
        <div class="form-group">
            <label for="location" class="form-label required">Location</label>
            <input type="text" id="location" name="location" class="form-input" value="<%= event.location %>" required placeholder="Event venue or online link">
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
                <label for="startDate" class="form-label required">Start Date & Time</label>
                <input type="datetime-local" id="startDate" name="startDate" class="form-input"
                       value="<%= event.date ? new Date(event.date.getTime() - event.date.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : '' %>" required>
            </div>
            <div class="form-group">
                <label for="endDate" class="form-label">End Date & Time</label>
                <input type="datetime-local" id="endDate" name="endDate" class="form-input"
                       value="<%= event.endDate ? new Date(event.endDate.getTime() - event.endDate.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : '' %>">
            </div>
        </div>

        <!-- Registration Link -->
        <div class="form-group">
            <label for="registrationLink" class="form-label">Registration Link</label>
            <input type="url" id="registrationLink" name="registrationLink" class="form-input"
                   value="<%= event.registrationLink || '' %>" placeholder="https://example.com/register">
            <p class="text-sm text-gray-500 mt-1">Optional link for event registration</p>
        </div>

        <!-- Enhanced Image Upload Section -->
        <div class="form-group">
            <label class="form-label">Event Image</label>

            <!-- Current Image Display -->
            <% if (event.image) { %>
            <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600 mb-3">Current Image:</p>
                <div class="flex items-start space-x-4">
                    <img src="<%= event.thumbnails && event.thumbnails.medium ? event.thumbnails.medium : event.image %>"
                         alt="Current event image"
                         class="w-32 h-32 object-cover rounded-lg border border-gray-200">
                    <div class="flex-1">
                        <p class="text-sm text-gray-700 mb-2">
                            <strong>Current:</strong> Event image uploaded
                        </p>
                        <div class="flex space-x-2">
                            <a href="<%= event.image %>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>View Full Size
                            </a>
                            <button type="button" onclick="removeCurrentImage()" class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash mr-1"></i>Remove Image
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <% } %>

        <!-- Event Image -->
        <div class="form-group">
            <label for="image" class="form-label">Event Image</label>
            <input type="file" id="image" name="image" accept="image/*" class="form-input">
            <p class="form-help">Upload an image for the event (JPG, PNG, GIF up to 5MB)</p>
        </div>

            <p class="text-sm text-gray-500 mt-2">
                <% if (event.image) { %>
                    Upload a new image to replace the current one, or leave empty to keep the current image.
                <% } else { %>
                    Upload an attractive image that represents your event.
                <% } %>
            </p>
        </div>

        <!-- Featured -->
        <div class="form-group">
            <div class="flex items-center">
                <input type="checkbox" id="featured" name="featured" value="true" class="form-checkbox" <%= event.featured ? 'checked' : '' %>>
                <label for="featured" class="ml-2 text-sm text-gray-700">Featured Event</label>
            </div>
        </div>

        <!-- Event Status Info -->
        <% if (event.isPast) { %>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-1"></i>
                <div>
                    <h4 class="text-yellow-800 font-medium">Past Event</h4>
                    <p class="text-yellow-700 text-sm">This event has already occurred.</p>
                </div>
            </div>
        </div>
        <% } %>
    </form>
</div>

<!-- Gallery Management Section -->
<div class="bg-white rounded-lg shadow-md p-6 mt-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-xl font-bold text-gray-900">Event Gallery</h2>
            <p class="text-sm text-gray-600 mt-1">Upload additional images for this event (optional)</p>
        </div>
        <div class="text-sm text-gray-500">
            <i class="fas fa-images mr-1"></i>
            <span id="galleryCount"><%= event.gallery ? event.gallery.length : 0 %></span> images
        </div>
    </div>

    <!-- Upload Section -->
    <div class="mb-6">
        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors" id="galleryDropZone">
            <div class="space-y-4">
                <div class="mx-auto w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cloud-upload-alt text-blue-600 text-xl"></i>
                </div>
                <div>
                    <label for="galleryImages" class="cursor-pointer">
                        <span class="text-blue-600 hover:text-blue-800 font-medium">Click to upload multiple images</span>
                        <span class="text-gray-600"> or drag and drop</span>
                    </label>
                    <input type="file" id="galleryImages" name="galleryImages" multiple accept="image/*" class="hidden">
                    <p class="text-xs text-gray-500 mt-2">PNG, JPG, GIF up to 5MB each (max 10 images at once)</p>
                </div>
            </div>
        </div>

        <!-- Upload Progress -->
        <div id="uploadProgress" class="hidden mt-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                    <span class="text-blue-800 text-sm">Uploading images...</span>
                </div>
                <div class="mt-2 bg-blue-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Gallery Images -->
    <div id="galleryContainer">
        <% if (event.gallery && event.gallery.length > 0) { %>
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <i class="fas fa-images mr-2"></i>
            Current Gallery Images (<span id="galleryCountDisplay"><%= event.gallery.length %></span>)
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <% event.gallery.forEach((image, index) => { %>
            <div class="gallery-item relative group" data-index="<%= index %>">
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-sm">
                    <img src="<%= image %>" alt="Gallery image <%= index + 1 %>"
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                </div>
                <button onclick="deleteGalleryImage('<%= event._id %>', '<%= index %>')"
                        class="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
                        title="Delete this image">
                    <i class="fas fa-trash text-xs"></i>
                </button>
                <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    <%= index + 1 %>
                </div>
            </div>
            <% }); %>
        </div>
        <% } else { %>
        <div id="emptyGallery" class="text-center py-12 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
            <i class="fas fa-images text-5xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No gallery images uploaded yet</p>
            <p class="text-sm mt-2">Upload some images using the section above to create a beautiful gallery for this event</p>
        </div>
        <% } %>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
        <a href="/admin/events" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        <div class="flex space-x-3">
            <button type="button" onclick="deleteEvent('<%= event._id %>')" class="btn btn-danger">
                <i class="fas fa-trash mr-2"></i>Delete
            </button>
            <button type="submit" class="btn btn-primary" id="updateBtn">
                <i class="fas fa-save mr-2"></i>Update Event
            </button>
        </div>
    </div>
</form>
</div>



<!-- Enhanced Script with Gallery Management -->
<script>
    function deleteEvent(eventId) {
        if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
            console.log('🗑️ Deleting event:', eventId);

            fetch(`/admin/events/${eventId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('Delete response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Delete response data:', data);
                if (data.success) {
                    showNotification('success', 'Event deleted successfully');
                    setTimeout(() => {
                        window.location.href = '/admin/events';
                    }, 1000);
                } else {
                    showNotification('error', data.error || 'Error deleting event');
                }
            })
            .catch(error => {
                console.error('Error deleting event:', error);
                showNotification('error', 'Error deleting event');
            });
        }
    }

    // Gallery Management Functions
    const galleryInput = document.getElementById('galleryImages');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const galleryContainer = document.getElementById('galleryContainer');
    const galleryCount = document.getElementById('galleryCount');
    const galleryCountDisplay = document.getElementById('galleryCountDisplay');

    // File input change handler
    if (galleryInput) {
        galleryInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                uploadGalleryImages(e.target.files);
            }
        });
    }

    // Drag and drop functionality
    const dropZone = document.getElementById('galleryDropZone');
    if (dropZone) {
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropZone.classList.add('border-blue-500', 'bg-blue-50');
        });

        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadGalleryImages(files);
            }
        });
    }

    // Upload gallery images function
    function uploadGalleryImages(files) {
        const formData = new FormData();

        // Add files to form data
        Array.from(files).forEach(file => {
            formData.append('galleryImages', file);
        });

        // Show progress
        if (uploadProgress) uploadProgress.classList.remove('hidden');
        if (progressBar) progressBar.style.width = '0%';

        fetch(`/admin/events/<%= event._id %>/gallery`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (progressBar) progressBar.style.width = '100%';
            return response.json();
        })
        .then(data => {
            if (uploadProgress) uploadProgress.classList.add('hidden');

            if (data.success) {
                showNotification('success', data.message);
                // Update gallery count
                if (galleryCount) galleryCount.textContent = data.totalImages;
                if (galleryCountDisplay) galleryCountDisplay.textContent = data.totalImages;
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification('error', data.error || 'Error uploading images');
            }
        })
        .catch(error => {
            if (uploadProgress) uploadProgress.classList.add('hidden');
            console.error('Error:', error);
            showNotification('error', 'Error uploading images');
        });

        // Reset file input
        if (galleryInput) galleryInput.value = '';
    }

    // Delete gallery image function
    function deleteGalleryImage(eventId, imageIndex) {
        if (confirm('Are you sure you want to delete this image?')) {
            console.log('🗑️ Deleting gallery image:', eventId, imageIndex);

            fetch(`/admin/events/${eventId}/gallery/${imageIndex}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('Delete gallery image response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Delete gallery image response data:', data);
                if (data.success) {
                    showNotification('success', 'Image deleted successfully');
                    // Update gallery count
                    if (galleryCount) galleryCount.textContent = data.remainingImages;
                    if (galleryCountDisplay) galleryCountDisplay.textContent = data.remainingImages;
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('error', data.error || 'Error deleting image');
                }
            })
            .catch(error => {
                console.error('Error deleting gallery image:', error);
                showNotification('error', 'Error deleting image');
            });
        }
    }

    // Show notification function
    function showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
</script>
