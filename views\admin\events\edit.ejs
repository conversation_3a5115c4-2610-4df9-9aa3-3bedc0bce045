<!-- Edit Event Form -->
<div class="admin-header">
    <h1 class="admin-title">
        <i class="fas fa-edit mr-2 text-yellow-600"></i>Edit Event
    </h1>
</div>

<div class="bg-white rounded-lg shadow-md p-6">
    <form id="editEventForm" action="/admin/events/<%= event._id %>" method="POST" enctype="multipart/form-data" class="space-y-6">
        <input type="hidden" name="_method" value="PUT">

        <!-- Title -->
        <div class="form-group">
            <label for="title" class="form-label required">Event Title</label>
            <input type="text" id="title" name="title" class="form-input" value="<%= event.title %>" required>
        </div>

        <!-- Description -->
        <div class="form-group">
            <label for="description" class="form-label required">Description</label>
            <textarea id="description" name="description" rows="5" class="form-textarea" required><%= event.description %></textarea>
        </div>

        <!-- Location -->
        <div class="form-group">
            <label for="location" class="form-label required">Location</label>
            <input type="text" id="location" name="location" class="form-input" value="<%= event.location %>" required>
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
                <label for="startDate" class="form-label required">Start Date & Time</label>
                <input type="datetime-local" id="startDate" name="startDate" class="form-input"
                       value="<%= event.date ? new Date(event.date.getTime() - event.date.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : '' %>" required>
            </div>
            <div class="form-group">
                <label for="endDate" class="form-label">End Date & Time</label>
                <input type="datetime-local" id="endDate" name="endDate" class="form-input"
                       value="<%= event.endDate ? new Date(event.endDate.getTime() - event.endDate.getTimezoneOffset() * 60000).toISOString().slice(0, 16) : '' %>">
            </div>
        </div>

        <!-- Registration Link -->
        <div class="form-group">
            <label for="registrationLink" class="form-label">Registration Link</label>
            <input type="url" id="registrationLink" name="registrationLink" class="form-input" 
                   value="<%= event.registrationLink || '' %>" placeholder="https://example.com/register">
        </div>

        <!-- Cover Image -->
        <div class="form-group">
            <label for="image" class="form-label">Cover Image</label>
            <input type="file" id="image" name="image" accept="image/*" class="form-input">
            <p class="form-help">Upload a new cover image (JPG, PNG, GIF up to 5MB) - leave empty to keep current image</p>
            <% if (event.image) { %>
            <div class="mt-3">
                <p class="text-sm text-gray-600 mb-2">Current cover image:</p>
                <img src="<%= event.image %>" alt="Current cover" class="w-32 h-32 object-cover rounded-lg border">
            </div>
            <% } %>
        </div>

        <!-- Featured Status -->
        <div class="form-group">
            <label class="flex items-center">
                <input type="checkbox" name="featured" value="true" class="form-checkbox" <%= event.featured ? 'checked' : '' %>>
                <span class="ml-2 text-sm font-medium text-gray-700">Featured Event</span>
            </label>
            <p class="form-help">Featured events appear prominently on the homepage</p>
        </div>

        <!-- Gallery Images -->
        <div class="form-group">
            <label for="galleryImages" class="form-label">Gallery Images</label>
            <input type="file" id="galleryImages" name="galleryImages" multiple accept="image/*" class="form-input">
            <p class="form-help">Upload multiple images for the event gallery (JPG, PNG, GIF up to 5MB each, max 10 images)</p>
            
            <!-- Upload Progress -->
            <div id="uploadProgress" class="hidden mt-3">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div class="flex items-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                        <span class="text-blue-800 text-sm">Uploading images...</span>
                    </div>
                    <div class="mt-2 bg-blue-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Gallery Images -->
        <% if (event.gallery && event.gallery.length > 0) { %>
        <div class="form-group">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Current Gallery Images (<%= event.gallery.length %>)</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <% event.gallery.forEach((image, index) => { %>
                <div class="relative group">
                    <img src="<%= image %>" alt="Gallery image <%= index + 1 %>" 
                         class="w-full h-32 object-cover rounded-lg border">
                    <button type="button" onclick="deleteGalleryImage('<%= event._id %>', '<%= index %>')"
                            class="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                    <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        <%= index + 1 %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="/admin/events" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Cancel
            </a>
            <div class="flex space-x-3">
                <button type="button" onclick="deleteEvent('<%= event._id %>')" class="btn btn-danger">
                    <i class="fas fa-trash mr-2"></i>Delete Event
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>Update Event
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Form submission handler
document.getElementById('editEventForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            showNotification('success', 'Event updated successfully');
            setTimeout(() => {
                window.location.href = '/admin/events';
            }, 1000);
        } else {
            throw new Error('Update failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', 'Error updating event');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Gallery upload handler
document.getElementById('galleryImages').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        uploadGalleryImages(e.target.files);
    }
});

function uploadGalleryImages(files) {
    const formData = new FormData();
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    
    // Add files to form data
    Array.from(files).forEach(file => {
        formData.append('galleryImages', file);
    });
    
    // Show progress
    uploadProgress.classList.remove('hidden');
    progressBar.style.width = '0%';
    
    fetch(`/admin/events/<%= event._id %>/gallery`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        progressBar.style.width = '100%';
        return response.json();
    })
    .then(data => {
        uploadProgress.classList.add('hidden');
        
        if (data.success) {
            showNotification('success', data.message);
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('error', data.error || 'Error uploading images');
        }
    })
    .catch(error => {
        uploadProgress.classList.add('hidden');
        console.error('Error:', error);
        showNotification('error', 'Error uploading images');
    });
    
    // Reset file input
    document.getElementById('galleryImages').value = '';
}

// Delete event function
function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        fetch(`/admin/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', 'Event deleted successfully');
                setTimeout(() => {
                    window.location.href = '/admin/events';
                }, 1000);
            } else {
                showNotification('error', data.error || 'Error deleting event');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Error deleting event');
        });
    }
}

// Delete gallery image function
function deleteGalleryImage(eventId, imageIndex) {
    if (confirm('Are you sure you want to delete this image?')) {
        fetch(`/admin/events/${eventId}/gallery/${imageIndex}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', 'Image deleted successfully');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('error', data.error || 'Error deleting image');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Error deleting image');
        });
    }
}

// Show notification function
function showNotification(type, message) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
