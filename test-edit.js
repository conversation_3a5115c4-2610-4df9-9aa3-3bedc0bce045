const axios = require('axios');

// Create axios instance
const client = axios.create({
  validateStatus: function (status) {
    return status >= 200 && status < 500; // Accept all responses
  }
});

async function testEventEdit() {
  try {
    console.log('Testing event edit functionality via HTTP...');

    // For testing, let's use a known event ID from our database
    // We'll use the event we updated in the previous test
    const eventId = '683946b3f706c53831623444'; // From our previous test

    console.log('Testing edit form submission for event ID:', eventId);

    // Submit the edit form directly (simulating what the browser would do)
    const formData = new URLSearchParams();
    formData.append('_method', 'PUT');
    formData.append('title', 'Test Event Updated via HTTP');
    formData.append('description', 'This event was updated via HTTP test');
    formData.append('location', 'Test Location Updated');
    formData.append('startDate', '2024-12-31T10:00');
    formData.append('endDate', '2024-12-31T12:00');
    formData.append('featured', 'true');
    formData.append('registrationLink', 'https://example.com/register');

    console.log('Submitting form data...');

    const editResponse = await client.post(`http://localhost:10000/admin/events/${eventId}`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('Edit response status:', editResponse.status);
    console.log('Edit response headers:', editResponse.headers);

    if (editResponse.status === 302) {
      console.log('✓ Edit successful - redirected to:', editResponse.headers.location);
    } else if (editResponse.status === 401 || editResponse.status === 403) {
      console.log('Authentication required - this is expected without login');
    } else {
      console.log('Edit response data (first 500 chars):', editResponse.data.substring(0, 500));
    }

  } catch (error) {
    console.error('Error testing event edit:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data (first 500 chars):', error.response.data ? error.response.data.substring(0, 500) : 'No data');
    }
  }
}

testEventEdit();
