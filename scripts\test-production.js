#!/usr/bin/env node

/**
 * Production Test Script
 * Tests the API endpoints to ensure everything is working correctly
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

class ProductionTester {
  constructor() {
    this.baseURL = process.env.BASE_URL || 'http://localhost:3000';
    this.apiURL = `${this.baseURL}/api`;
    this.passed = 0;
    this.failed = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
    
    switch (type) {
      case 'error':
        console.error(`${prefix} ${message}`);
        break;
      case 'success':
        console.log(`${prefix} ${message}`);
        break;
      default:
        console.log(`${prefix} ${message}`);
    }
  }

  async testEndpoint(name, url, expectedStatus = 200) {
    try {
      this.log(`Testing ${name}: ${url}`, 'info');
      const response = await axios.get(url, { timeout: 10000 });
      
      if (response.status === expectedStatus) {
        this.log(`✓ ${name} - Status: ${response.status}`, 'success');
        this.passed++;
        return true;
      } else {
        this.log(`✗ ${name} - Expected: ${expectedStatus}, Got: ${response.status}`, 'error');
        this.failed++;
        return false;
      }
    } catch (error) {
      this.log(`✗ ${name} - Error: ${error.message}`, 'error');
      this.failed++;
      return false;
    }
  }

  async testAPIHealth() {
    return this.testEndpoint('API Health Check', `${this.apiURL}/health`);
  }

  async testAPIDocumentation() {
    return this.testEndpoint('API Documentation', `${this.apiURL}/docs`);
  }

  async testPublicEndpoints() {
    const endpoints = [
      { name: 'Projects API', url: `${this.apiURL}/projects` },
      { name: 'Blog API', url: `${this.apiURL}/blog` },
      { name: 'Events API', url: `${this.apiURL}/events` },
      { name: 'Resources API', url: `${this.apiURL}/resources` }
    ];

    for (const endpoint of endpoints) {
      await this.testEndpoint(endpoint.name, endpoint.url);
    }
  }

  async testWebPages() {
    const pages = [
      { name: 'Home Page', url: `${this.baseURL}/` },
      { name: 'Projects Page', url: `${this.baseURL}/projects` },
      { name: 'Blog Page', url: `${this.baseURL}/blog` },
      { name: 'Events Page', url: `${this.baseURL}/events` },
      { name: 'Resources Page', url: `${this.baseURL}/resources` },
      { name: 'Contact Page', url: `${this.baseURL}/contact` }
    ];

    for (const page of pages) {
      await this.testEndpoint(page.name, page.url);
    }
  }

  async testContactSubmission() {
    try {
      this.log('Testing Contact Form Submission', 'info');
      
      const testData = {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Submission',
        message: 'This is a test message from the production test script.'
      };

      const response = await axios.post(`${this.apiURL}/contacts`, testData, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

      if (response.status === 201 || response.status === 200) {
        this.log('✓ Contact Form Submission - Success', 'success');
        this.passed++;
        return true;
      } else {
        this.log(`✗ Contact Form Submission - Status: ${response.status}`, 'error');
        this.failed++;
        return false;
      }
    } catch (error) {
      this.log(`✗ Contact Form Submission - Error: ${error.message}`, 'error');
      this.failed++;
      return false;
    }
  }

  async test404Handling() {
    try {
      this.log('Testing 404 Handling', 'info');
      await axios.get(`${this.baseURL}/nonexistent-page`);
      this.log('✗ 404 Handling - Should have returned 404', 'error');
      this.failed++;
      return false;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        this.log('✓ 404 Handling - Correctly returns 404', 'success');
        this.passed++;
        return true;
      } else {
        this.log(`✗ 404 Handling - Unexpected error: ${error.message}`, 'error');
        this.failed++;
        return false;
      }
    }
  }

  async testAPINotFound() {
    try {
      this.log('Testing API 404 Handling', 'info');
      await axios.get(`${this.apiURL}/nonexistent-endpoint`);
      this.log('✗ API 404 Handling - Should have returned 404', 'error');
      this.failed++;
      return false;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        this.log('✓ API 404 Handling - Correctly returns 404', 'success');
        this.passed++;
        return true;
      } else {
        this.log(`✗ API 404 Handling - Unexpected error: ${error.message}`, 'error');
        this.failed++;
        return false;
      }
    }
  }

  generateReport() {
    this.log('\n=== PRODUCTION TEST REPORT ===', 'info');
    this.log(`✓ Tests Passed: ${this.passed}`, 'success');
    
    if (this.failed > 0) {
      this.log(`✗ Tests Failed: ${this.failed}`, 'error');
      this.log('\n❌ Some tests failed. Please check the issues above.', 'error');
    } else {
      this.log('\n🎉 All tests passed! Your application is working correctly.', 'success');
    }

    const total = this.passed + this.failed;
    const successRate = total > 0 ? ((this.passed / total) * 100).toFixed(1) : 0;
    this.log(`Success Rate: ${successRate}%`, 'info');

    return this.failed === 0;
  }

  async run() {
    this.log('Starting production tests...', 'info');
    this.log(`Testing against: ${this.baseURL}`, 'info');
    
    // Test API endpoints
    await this.testAPIHealth();
    await this.testAPIDocumentation();
    await this.testPublicEndpoints();
    
    // Test web pages
    await this.testWebPages();
    
    // Test functionality
    await this.testContactSubmission();
    
    // Test error handling
    await this.test404Handling();
    await this.testAPINotFound();
    
    return this.generateReport();
  }
}

// Run the tests
if (require.main === module) {
  const tester = new ProductionTester();
  tester.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = ProductionTester;
