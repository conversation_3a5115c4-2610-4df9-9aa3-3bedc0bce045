<!-- Search Results Page -->

<!-- Hero Section -->
<section class="py-12 bg-gradient-to-br from-blue-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <img src="/images/logo.jpg" alt="Science Club Turbat Logo" class="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 object-contain rounded-lg shadow-lg">
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-search mr-3 text-blue-600"></i>Search Results
            </h1>
            <% if (query) { %>
                <p class="text-xl text-gray-600 mb-6">
                    Found <strong><%= total %></strong> results for "<strong><%= query %></strong>"
                </p>
            <% } else { %>
                <p class="text-xl text-gray-600 mb-6">
                    Search our entire website for projects, resources, and events
                </p>
            <% } %>
        </div>
    </div>
</section>

<!-- Search Form -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" action="/search" class="max-w-4xl mx-auto">
            <div class="flex flex-col md:flex-row gap-4">
                <!-- Search Input -->
                <div class="flex-1 relative">
                    <input
                        type="text"
                        name="q"
                        value="<%= query %>"
                        placeholder="Search for projects, articles, resources..."
                        class="w-full px-6 py-4 pl-12 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        autofocus
                    >
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 text-xl"></i>
                    </div>
                </div>

                <!-- Type Filter -->
                <div class="md:w-48">
                    <select name="type" class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all" <%= selectedType === 'all' ? 'selected' : '' %>>All Types</option>
                        <option value="projects" <%= selectedType === 'projects' ? 'selected' : '' %>>Projects</option>
                        <option value="blog" <%= selectedType === 'blog' ? 'selected' : '' %>>Blog Posts</option>
                        <option value="resources" <%= selectedType === 'resources' ? 'selected' : '' %>>Resources</option>
                        <option value="events" <%= selectedType === 'events' ? 'selected' : '' %>>Events</option>
                    </select>
                </div>

                <!-- Search Button -->
                <button type="submit" class="btn btn-primary btn-lg px-8 py-4 whitespace-nowrap">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Error Message -->
<% if (typeof error !== 'undefined' && error) { %>
<section class="py-6 bg-red-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span><%= error %></span>
            </div>
        </div>
    </div>
</section>
<% } %>

<!-- Search Results -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <% if (query && results.length > 0) { %>
            <!-- Results Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <% results.forEach(result => { %>
                    <div class="search-result-card bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                        <!-- Result Type Badge -->
                        <div class="p-4 pb-0">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                <% if (result.type === 'project') { %>bg-blue-100 text-blue-800<% } %>

                                <% if (result.type === 'resource') { %>bg-purple-100 text-purple-800<% } %>
                                <% if (result.type === 'event') { %>bg-orange-100 text-orange-800<% } %>
                            ">
                                <% if (result.type === 'project') { %><i class="fas fa-flask mr-1"></i>Project<% } %>

                                <% if (result.type === 'resource') { %><i class="fas fa-book mr-1"></i>Resource<% } %>
                                <% if (result.type === 'event') { %><i class="fas fa-calendar mr-1"></i>Event<% } %>
                            </span>
                        </div>

                        <!-- Result Content -->
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                                <a href="<%= result.url %>" class="hover:text-blue-600 transition-colors">
                                    <%= result.title %>
                                </a>
                            </h3>

                            <p class="text-gray-600 mb-4 line-clamp-3">
                                <%= result.description %>
                            </p>

                            <!-- Result Meta -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-1"></i>
                                    <%= new Date(result.date).toLocaleDateString() %>
                                </div>
                                <% if (result.authors) { %>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-1"></i>
                                        <%= result.authors[0] %>
                                    </div>
                                <% } %>
                                <% if (result.author) { %>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-1"></i>
                                        <%= result.author %>
                                    </div>
                                <% } %>
                                <% if (result.location) { %>
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        <%= result.location %>
                                    </div>
                                <% } %>
                            </div>

                            <!-- Read More Button -->
                            <a href="<%= result.url %>" class="btn btn-outline btn-sm w-full">
                                <i class="fas fa-arrow-right mr-2"></i>
                                <% if (result.type === 'project') { %>View Project<% } %>

                                <% if (result.type === 'resource') { %>Access Resource<% } %>
                                <% if (result.type === 'event') { %>Event Details<% } %>
                            </a>
                        </div>
                    </div>
                <% }) %>
            </div>

            <!-- Pagination -->
            <% if (totalPages > 1) { %>
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        <% if (currentPageNum > 1) { %>
                            <a href="/search?q=<%= encodeURIComponent(query) %>&type=<%= selectedType %>&page=<%= currentPageNum - 1 %>"
                               class="px-4 py-2 text-gray-500 hover:text-blue-600 transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <% } %>

                        <% for (let i = Math.max(1, currentPageNum - 2); i <= Math.min(totalPages, currentPageNum + 2); i++) { %>
                            <a href="/search?q=<%= encodeURIComponent(query) %>&type=<%= selectedType %>&page=<%= i %>"
                               class="px-4 py-2 rounded-lg <%= i === currentPageNum ? 'bg-blue-600 text-white' : 'text-gray-500 hover:text-blue-600' %> transition-colors">
                                <%= i %>
                            </a>
                        <% } %>

                        <% if (currentPageNum < totalPages) { %>
                            <a href="/search?q=<%= encodeURIComponent(query) %>&type=<%= selectedType %>&page=<%= currentPageNum + 1 %>"
                               class="px-4 py-2 text-gray-500 hover:text-blue-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <% } %>
                    </nav>
                </div>
            <% } %>

        <% } else if (query) { %>
            <!-- No Results -->
            <div class="text-center py-16">
                <i class="fas fa-search text-gray-300 text-6xl mb-6"></i>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No results found</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    We couldn't find anything matching "<strong><%= query %></strong>".
                    Try different keywords or browse our categories below.
                </p>

                <!-- Suggestions -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                    <a href="/projects" class="btn btn-outline">
                        <i class="fas fa-flask mr-2"></i>Projects
                    </a>

                    <a href="/resources" class="btn btn-outline">
                        <i class="fas fa-book mr-2"></i>Resources
                    </a>
                    <a href="/events" class="btn btn-outline">
                        <i class="fas fa-calendar mr-2"></i>Events
                    </a>
                </div>
            </div>
        <% } else { %>
            <!-- Search Landing -->
            <div class="text-center py-16">
                <i class="fas fa-search text-blue-600 text-6xl mb-6"></i>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">What are you looking for?</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    Search through our collection of research projects, educational resources, and upcoming events.
                </p>
            </div>
        <% } %>
    </div>
</section>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-result-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.search-result-card .p-6 {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.search-result-card .btn {
    margin-top: auto;
}
</style>
