/**
 * Science Club API Client Example
 * This file demonstrates how to interact with the Science Club API from the frontend
 */

class ScienceClubAPI {
  constructor(baseUrl = '/api') {
    this.baseUrl = baseUrl;
    this.token = localStorage.getItem('scienceclub_token');
  }

  // Helper method to make API requests
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // Add authorization header if token exists
    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }
      
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(email, password) {
    const data = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });
    
    if (data.success && data.data.token) {
      this.token = data.data.token;
      localStorage.setItem('scienceclub_token', this.token);
    }
    
    return data;
  }

  async logout() {
    if (this.token) {
      await this.request('/auth/logout', { method: 'POST' });
    }
    
    this.token = null;
    localStorage.removeItem('scienceclub_token');
  }

  async getCurrentUser() {
    return await this.request('/auth/me');
  }

  // Projects methods
  async getProjects(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/projects${queryString ? '?' + queryString : ''}`);
  }

  async getFeaturedProjects(limit = 6) {
    return await this.request(`/projects/featured?limit=${limit}`);
  }

  async getProject(id) {
    return await this.request(`/projects/${id}`);
  }

  async createProject(projectData) {
    return await this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
  }

  async updateProject(id, projectData) {
    return await this.request(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(projectData)
    });
  }

  async deleteProject(id) {
    return await this.request(`/projects/${id}`, {
      method: 'DELETE'
    });
  }

  // Blog methods
  async getBlogPosts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/blog${queryString ? '?' + queryString : ''}`);
  }

  async getBlogPost(id) {
    return await this.request(`/blog/${id}`);
  }

  // Events methods
  async getEvents(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/events${queryString ? '?' + queryString : ''}`);
  }

  async getUpcomingEvents(limit = 6) {
    return await this.request(`/events/upcoming?limit=${limit}`);
  }

  // Resources methods
  async getResources(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/resources${queryString ? '?' + queryString : ''}`);
  }

  async getResourcesByCategory(category) {
    return await this.request(`/resources/category/${category}`);
  }

  // Contact methods
  async submitContact(contactData) {
    return await this.request('/contacts', {
      method: 'POST',
      body: JSON.stringify(contactData)
    });
  }

  // Search methods
  async search(query, params = {}) {
    const searchParams = new URLSearchParams({ q: query, ...params });
    return await this.request(`/search?${searchParams.toString()}`);
  }

  async getSearchSuggestions(query) {
    return await this.request(`/search/suggestions?q=${encodeURIComponent(query)}`);
  }

  // Health check
  async healthCheck() {
    return await this.request('/health');
  }
}

// Example usage functions
async function exampleUsage() {
  const api = new ScienceClubAPI();

  try {
    // Check API health
    console.log('🔍 Checking API health...');
    const health = await api.healthCheck();
    console.log('API Status:', health.data.status);

    // Get featured projects
    console.log('📚 Getting featured projects...');
    const featuredProjects = await api.getFeaturedProjects(3);
    console.log('Featured Projects:', featuredProjects.data);

    // Get upcoming events
    console.log('📅 Getting upcoming events...');
    const upcomingEvents = await api.getUpcomingEvents(3);
    console.log('Upcoming Events:', upcomingEvents.data);

    // Search example
    console.log('🔍 Searching for "science"...');
    const searchResults = await api.search('science', { limit: 5 });
    console.log('Search Results:', searchResults.data);

    // Submit contact form example
    console.log('📧 Submitting contact form...');
    const contactResult = await api.submitContact({
      name: 'API Test User',
      email: '<EMAIL>',
      subject: 'API Test',
      message: 'This is a test message from the API client'
    });
    console.log('Contact Submission:', contactResult.success ? 'Success' : 'Failed');

  } catch (error) {
    console.error('Example Error:', error.message);
  }
}

// DOM manipulation examples
function displayProjects(projects) {
  const container = document.getElementById('projects-container');
  if (!container) return;

  container.innerHTML = projects.map(project => `
    <div class="project-card">
      <h3>${project.title}</h3>
      <p>${project.description.substring(0, 100)}...</p>
      <div class="project-authors">
        Authors: <AUTHORS>
      </div>
      <div class="project-tags">
        ${project.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
      </div>
    </div>
  `).join('');
}

function displayEvents(events) {
  const container = document.getElementById('events-container');
  if (!container) return;

  container.innerHTML = events.map(event => `
    <div class="event-card">
      <h3>${event.title}</h3>
      <p>${event.description.substring(0, 100)}...</p>
      <div class="event-details">
        <span class="event-date">${new Date(event.date).toLocaleDateString()}</span>
        <span class="event-location">${event.location}</span>
      </div>
    </div>
  `).join('');
}

// Initialize API client when page loads
document.addEventListener('DOMContentLoaded', async () => {
  window.scienceClubAPI = new ScienceClubAPI();
  
  // Example: Load featured projects on page load
  try {
    const featuredProjects = await window.scienceClubAPI.getFeaturedProjects(6);
    if (featuredProjects.success) {
      displayProjects(featuredProjects.data);
    }
  } catch (error) {
    console.error('Failed to load featured projects:', error);
  }

  // Example: Load upcoming events
  try {
    const upcomingEvents = await window.scienceClubAPI.getUpcomingEvents(6);
    if (upcomingEvents.success) {
      displayEvents(upcomingEvents.data);
    }
  } catch (error) {
    console.error('Failed to load upcoming events:', error);
  }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ScienceClubAPI;
}
