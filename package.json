{"name": "scienceclub", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "seed": "node scripts/seed.js", "deploy": "node scripts/deploy.js", "production-check": "node scripts/production-setup.js", "test": "node scripts/test-production.js", "test:api": "node scripts/testApi.js", "test:production": "node scripts/test-production.js", "test:admin": "node scripts/test-admin-dashboard.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "axios-cookiejar-support": "^6.0.2", "bcrypt": "^6.0.0", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "method-override": "^3.0.0", "mongoose": "^8.15.1", "multer": "^2.0.0", "nodemailer": "^7.0.3", "sharp": "^0.34.2", "tailwindcss": "^4.1.8", "tough-cookie": "^5.1.2"}, "devDependencies": {"nodemon": "^3.1.10"}}