<!-- Events Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-purple-600 to-blue-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <img src="/images/logo.jpg" alt="Science Club Turbat Logo" class="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 object-contain rounded-lg shadow-lg">
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Science Events</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Join us for workshops, seminars, conferences, and networking events
            </p>
        </div>
    </div>
</section>

<!-- Upcoming Events Section -->
<% if (upcomingEvents && upcomingEvents.length > 0) { %>
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Upcoming Events</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% upcomingEvents.forEach(event => { %>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <% if (event.image) { %>
                            <img src="<%= event.image %>" alt="<%= event.title %>" class="w-full h-48 object-cover">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                                <i class="fas fa-calendar text-white text-3xl"></i>
                            </div>
                        <% } %>

                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">
                                <%= new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) %>
                            </div>

                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                 <%= event.title %>
                            </h3>

                            <p class="text-gray-600 text-sm mb-4">
                                <%= event.description %>
                            </p>

                            <div class="flex flex-wrap items-center justify-between text-sm text-gray-500">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-clock mr-1"></i>
                                    <%= new Date(event.date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) %>
                                </div>
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <%= event.location %>
                                </div>
                                <% if (event.organizer) { %>
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-user mr-1"></i>
                                        <%= event.organizer %>
                                    </div>
                                <% } %>
                            </div>

                            <div class="flex items-center justify-between mt-4">
                              

                                <% if (event.registrationLink) { %>
                                    <a href="<%= event.registrationLink %>" target="_blank" class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Register <i class="fas fa-external-link-alt ml-1"></i>
                                    </a>
                                <% } %>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        </div>
    </section>
<% } else { %>
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-calendar text-gray-400 text-4xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Upcoming Events</h3>
            <p class="text-gray-600 mb-8">
                We don't have any upcoming events scheduled at the moment.
                Check back soon for exciting workshops and seminars!
            </p>
            <a href="/contact" class="btn btn-primary">
                <i class="fas fa-envelope mr-2"></i>Get Notified
            </a>
        </div>
    </section>
<% } %>

<!-- Past Events Section -->
<% if (pastEvents && pastEvents.length > 0) { %>
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Past Events</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% pastEvents.slice(0, 6).forEach(event => { %>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <% if (event.image) { %>
                            <img src="<%= event.image %>" alt="<%= event.title %>" class="w-full h-48 object-cover">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center">
                                <i class="fas fa-calendar text-white text-3xl"></i>
                            </div>
                        <% } %>

                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">
                                <%= new Date(event.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) %>
                            </div>

                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <%= event.title %>
                            </h3>

                            <p class="text-gray-600 text-sm mb-4">
                                <%=  event.description %>
                            </p>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <%= event.location %>
                                </span>
                               
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>

            <% if (pastEvents.length > 6) { %>
                <div class="text-center mt-8">
                    <button class="btn btn-outline" onclick="loadMorePastEvents()">
                        <i class="fas fa-plus mr-2"></i>Load More Past Events
                    </button>
                </div>
            <% } %>
        </div>
    </section>
<% } %>



<section class="py-12 sm:py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Want to Host an Event?</h2>
        <p class="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 leading-relaxed">
             Have an idea for a workshop, seminar, or conference? We'd love to help you organize it!
        </p>
        <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
            <a href="/contact" class="btn btn-primary btn-lg w-full sm:w-auto">
                 <i class="fas fa-lightbulb mr-2"></i>
               Propose an Event
            </a>
            <a href="/about" class="btn btn-outline btn-lg w-full sm:w-auto">
                 <i class="fas fa-users mr-2"></i>
              Join Our Team
            </a>
        </div>
    </div>
</section>


<script>
function loadMorePastEvents() {
    // This would typically load more events via AJAX
    // For now, we'll just show a message
    alert('Loading more past events... (This would be implemented with AJAX)');
}
</script>
