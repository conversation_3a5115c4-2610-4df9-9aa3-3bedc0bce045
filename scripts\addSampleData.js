const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { Project, Event, Resource, User } = require('../models');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/scienceclub');
    console.log('MongoDB connected successfully');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

const addSampleData = async () => {
  try {
    // Clear existing data
    await Project.deleteMany({});
    await Event.deleteMany({});
    await Resource.deleteMany({});

    console.log('Cleared existing data');

    // Create a sample user
    let sampleUser = await User.findOne({ email: '<EMAIL>' });
    if (!sampleUser) {
      sampleUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin'
      });
      await sampleUser.save();
      console.log('Created sample user');
    }

    // Add sample projects
    const projects = [
      {
        title: 'Climate Change Research Project',
        description: 'A comprehensive study on local climate patterns and their impact on biodiversity in the Makran region.',
        authors: ['Dr. Sarah Johnson', 'Ahmed Ali'],
        tags: ['climate', 'environment', 'research'],
        featured: true,
        image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?w=800&q=80'
      },
      {
        title: 'Marine Biology Survey',
        description: 'Documenting marine life diversity along the Balochistan coast.',
        authors: ['Dr. Maria Khan', 'Hassan Baloch'],
        tags: ['marine', 'biology', 'survey'],
        featured: false,
        image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&q=80'
      },
      {
        title: 'Renewable Energy Solutions',
        description: 'Exploring solar and wind energy potential in rural Balochistan.',
        authors: ['Eng. Fatima Shah', 'Ali Raza'],
        tags: ['energy', 'renewable', 'technology'],
        featured: true,
        image: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=800&q=80'
      }
    ];

    await Project.insertMany(projects);
    console.log('Added sample projects');



    // Add sample events
    const currentDate = new Date();
    const events = [
      {
        title: 'Annual Science Fair 2024',
        description: 'Join us for our annual science fair featuring student projects, research presentations, and interactive demonstrations.',
        date: new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(currentDate.getTime() + 32 * 24 * 60 * 60 * 1000), // 32 days from now
        location: 'University of Turbat',
        organizer: 'Science Club Turbat',
        featured: true,
        isPast: false,
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&q=80'
      },
      {
        title: 'Workshop: Introduction to Data Science',
        description: 'Learn the basics of data analysis and visualization using Python and R.',
        date: new Date(currentDate.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        endDate: new Date(currentDate.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        location: 'Science Club Lab',
        organizer: 'Science Club Turbat',
        featured: false,
        isPast: false,
        image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80'
      },
      {
        title: 'Guest Lecture: Climate Change Adaptation',
        description: 'Dr. Ahmed Hassan discussed climate change adaptation strategies for coastal communities.',
        date: new Date(currentDate.getTime() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
        endDate: new Date(currentDate.getTime() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
        location: 'Main Auditorium',
        organizer: 'Science Club Turbat',
        featured: true,
        isPast: true,
        image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?w=800&q=80'
      },
      {
        title: 'Marine Biology Field Trip',
        description: 'Students explored marine ecosystems along the Makran coast and collected samples for research.',
        date: new Date(currentDate.getTime() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
        endDate: new Date(currentDate.getTime() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
        location: 'Gwadar Coast',
        organizer: 'Science Club Turbat',
        featured: false,
        isPast: true,
        image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&q=80'
      }
    ];

    await Event.insertMany(events);
    console.log('Added sample events');

    // Add sample resources
    const resources = [
      {
        title: 'Introduction to Research Methodology',
        description: 'A comprehensive guide to conducting scientific research.',
        type: 'pdf',
        url: 'https://example.com/research-methodology.pdf',
        category: 'Research',
        tags: ['research', 'methodology', 'guide'],
        featured: true
      },
      {
        title: 'Climate Data Visualization Tools',
        description: 'Online tools for visualizing climate data and trends.',
        type: 'link',
        url: 'https://climatedata.org',
        category: 'Tools',
        tags: ['climate', 'data', 'visualization'],
        featured: false
      },
      {
        title: 'Marine Biology Field Guide',
        description: 'Video series on identifying marine species in the Arabian Sea.',
        type: 'video',
        url: 'https://youtube.com/marine-biology-guide',
        category: 'Education',
        tags: ['marine', 'biology', 'field guide'],
        featured: true
      }
    ];

    await Resource.insertMany(resources);
    console.log('Added sample resources');

    console.log('Sample data added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error adding sample data:', error);
    process.exit(1);
  }
};

// Run the script
connectDB().then(() => {
  addSampleData();
});
