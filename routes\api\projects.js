const express = require('express');
const router = express.Router();
const Project = require('../../models/Project');
const ApiResponse = require('../../utils/apiResponse');
const { verifyToken, requireEditor, optionalAuth } = require('../../middleware/apiAuth');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

/**
 * @route   GET /api/projects
 * @desc    Get all projects with pagination
 * @access  Public
 */
router.get('/',
  validationChains.getPaginated,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query
      const query = {};

      // Filter by featured if specified
      if (req.query.featured !== undefined) {
        query.featured = req.query.featured === 'true';
      }

      // Filter by tags if specified
      if (req.query.tags) {
        const tags = Array.isArray(req.query.tags) ? req.query.tags : [req.query.tags];
        query.tags = { $in: tags };
      }

      // Search by title or description
      if (req.query.search) {
        query.$or = [
          { title: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const projects = await Project.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Project.countDocuments(query);
      const totalPages = Math.ceil(total / limit);

      const pagination = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      ApiResponse.paginated(res, projects, pagination, 'Projects retrieved successfully');

    } catch (error) {
      console.error('Get projects error:', error);
      ApiResponse.error(res, 'Failed to retrieve projects');
    }
  }
);

/**
 * @route   GET /api/projects/featured
 * @desc    Get featured projects
 * @access  Public
 */
router.get('/featured', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;

    const projects = await Project.find({ featured: true })
      .sort({ createdAt: -1 })
      .limit(limit);

    ApiResponse.success(res, projects, 'Featured projects retrieved successfully');

  } catch (error) {
    console.error('Get featured projects error:', error);
    ApiResponse.error(res, 'Failed to retrieve featured projects');
  }
});

/**
 * @route   GET /api/projects/tags/all
 * @desc    Get all unique tags
 * @access  Public
 */
router.get('/tags/all', async (req, res) => {
  try {
    const tags = await Project.distinct('tags');
    ApiResponse.success(res, tags, 'Tags retrieved successfully');
  } catch (error) {
    console.error('Get tags error:', error);
    ApiResponse.error(res, 'Failed to retrieve tags');
  }
});

/**
 * @route   GET /api/projects/:id
 * @desc    Get project by ID
 * @access  Public
 */
router.get('/:id',
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const project = await Project.findById(req.params.id);

      if (!project) {
        return ApiResponse.notFound(res, 'Project not found');
      }

      ApiResponse.success(res, project, 'Project retrieved successfully');

    } catch (error) {
      console.error('Get project error:', error);
      ApiResponse.error(res, 'Failed to retrieve project');
    }
  }
);

/**
 * @route   POST /api/projects
 * @desc    Create new project
 * @access  Private (Editor/Admin)
 */
router.post('/',
  verifyToken,
  requireEditor,
  validationChains.createProject,
  handleValidationErrors,
  async (req, res) => {
    try {
      const projectData = {
        title: req.body.title,
        description: req.body.description,
        authors: req.body.authors,
        tags: req.body.tags || [],
        featured: req.body.featured || false
      };

      // Handle file uploads if present
      if (req.body.image) {
        projectData.image = req.body.image;
      }
      if (req.body.pdfFile) {
        projectData.pdfFile = req.body.pdfFile;
      }

      const project = new Project(projectData);
      await project.save();

      ApiResponse.success(res, project, 'Project created successfully', 201);

    } catch (error) {
      console.error('Create project error:', error);
      ApiResponse.error(res, 'Failed to create project');
    }
  }
);

/**
 * @route   PUT /api/projects/:id
 * @desc    Update project
 * @access  Private (Editor/Admin)
 */
router.put('/:id',
  verifyToken,
  requireEditor,
  validationChains.updateProject,
  handleValidationErrors,
  async (req, res) => {
    try {
      const project = await Project.findById(req.params.id);

      if (!project) {
        return ApiResponse.notFound(res, 'Project not found');
      }

      // Update fields
      const allowedUpdates = ['title', 'description', 'authors', 'tags', 'featured', 'image', 'pdfFile'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          project[field] = req.body[field];
        }
      });

      await project.save();

      ApiResponse.success(res, project, 'Project updated successfully');

    } catch (error) {
      console.error('Update project error:', error);
      ApiResponse.error(res, 'Failed to update project');
    }
  }
);

/**
 * @route   DELETE /api/projects/:id
 * @desc    Delete project
 * @access  Private (Editor/Admin)
 */
router.delete('/:id',
  verifyToken,
  requireEditor,
  validationChains.getId,
  handleValidationErrors,
  async (req, res) => {
    try {
      const project = await Project.findById(req.params.id);

      if (!project) {
        return ApiResponse.notFound(res, 'Project not found');
      }

      await Project.findByIdAndDelete(req.params.id);

      ApiResponse.success(res, null, 'Project deleted successfully');

    } catch (error) {
      console.error('Delete project error:', error);
      ApiResponse.error(res, 'Failed to delete project');
    }
  }
);

module.exports = router;
