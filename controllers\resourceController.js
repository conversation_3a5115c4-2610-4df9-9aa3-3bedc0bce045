const apiClient = require('../utils/apiClient');
const { validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const Resource = require('../models/Resource');

// Helper function to get auth token from session
const getAuthToken = (req) => {
  return req.session.authToken || req.session.user?.token;
};

// Get all published resources
exports.getAllResources = async (req, res) => {
  try {
    const response = await apiClient.getResources();

    if (!response.success) {
      throw new Error(response.message);
    }

    const resources = response.data.resources || response.data || [];

    res.render('pages/resources/index', {
      title: 'Resources',
      currentPage: 'resources',
      resources
    });
  } catch (error) {
    console.error('Error fetching resources:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching resources'
    });
  }
};

// Get resources by category
exports.getResourcesByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const response = await apiClient.getResourcesByCategory(category);

    if (!response.success) {
      throw new Error(response.message);
    }

    const resources = response.data.resources || response.data || [];

    res.render('pages/resources/category', {
      title: `${category} Resources`,
      category,
      resources
    });
  } catch (error) {
    console.error('Error fetching resources by category:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'Error fetching resources'
    });
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/resources');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|ppt|pptx|xls|xlsx|zip|rar/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    if (extname) {
      return cb(null, true);
    }
    cb(new Error('Invalid file type!'));
  }
}).array('files', 5);

// Get all resources for admin dashboard
exports.getAdminResources = async (req, res) => {
  try {
    const token = getAuthToken(req);
    const response = await apiClient.getResources({ admin: true });

    if (!response.success) {
      throw new Error(response.message);
    }

    const resources = response.data.resources || response.data || [];

    res.render('admin/resources/index', {
      resources,
      title: 'Manage Resources',
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching admin resources:', error);
    req.flash('error', 'Error fetching resources');
    res.redirect('/admin/dashboard');
  }
};

// Show create resource form
exports.getCreateResourceForm = (req, res) => {
  res.render('admin/resources/create', {
    title: 'Create New Resource',
    user: req.session.user
  });
};

// Create new resource
exports.createResource = async (req, res) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        req.flash('error', err.message);
        return res.redirect('/admin/resources/create');
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        req.flash('error', errors.array()[0].msg);
        return res.redirect('/admin/resources/create');
      }

      // Handle file upload or URL based on type
      let url = req.body.url;
      let filePath = null;

      if (req.body.type === 'pdf' && req.file) {
        filePath = `/uploads/resources/${req.file.filename}`;
        url = filePath; // Use file path as URL for PDF files
      }

      const resource = new Resource({
        title: req.body.title,
        description: req.body.description,
        type: req.body.type,
        url: url,
        filePath: filePath,
        category: req.body.category,
        tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [],
        featured: req.body.featured === 'true'
      });

      await resource.save();
      req.flash('success', 'Resource created successfully');
      res.redirect('/admin/resources');
    });
  } catch (error) {
    req.flash('error', 'Error creating resource');
    res.redirect('/admin/resources/create');
  }
};

// Show edit resource form
exports.getEditResourceForm = async (req, res) => {
  try {
    const resource = await Resource.findById(req.params.id);
    if (!resource) {
      req.flash('error', 'Resource not found');
      return res.redirect('/admin/resources');
    }

    res.render('admin/resources/edit', {
      resource,
      title: 'Edit Resource',
      user: req.session.user
    });
  } catch (error) {
    req.flash('error', 'Error fetching resource');
    res.redirect('/admin/resources');
  }
};

// Update resource
exports.updateResource = async (req, res) => {
  try {
    upload(req, res, async (err) => {
      if (err) {
        req.flash('error', err.message);
        return res.redirect(`/admin/resources/${req.params.id}/edit`);
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        req.flash('error', errors.array()[0].msg);
        return res.redirect(`/admin/resources/${req.params.id}/edit`);
      }

      const resource = await Resource.findById(req.params.id);
      if (!resource) {
        req.flash('error', 'Resource not found');
        return res.redirect('/admin/resources');
      }

      // Handle file upload or URL based on type
      let url = req.body.url || resource.url;
      let filePath = resource.filePath;

      if (req.body.type === 'pdf' && req.file) {
        filePath = `/uploads/resources/${req.file.filename}`;
        url = filePath; // Use file path as URL for PDF files
      }

      resource.title = req.body.title;
      resource.description = req.body.description;
      resource.type = req.body.type;
      resource.url = url;
      resource.filePath = filePath;
      resource.category = req.body.category;
      resource.tags = req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : resource.tags;
      resource.featured = req.body.featured === 'true';

      await resource.save();
      req.flash('success', 'Resource updated successfully');
      res.redirect('/admin/resources');
    });
  } catch (error) {
    req.flash('error', 'Error updating resource');
    res.redirect(`/admin/resources/${req.params.id}/edit`);
  }
};

// Delete resource
exports.deleteResource = async (req, res) => {
  try {
    const resource = await Resource.findById(req.params.id);
    if (!resource) {
      return res.status(404).json({ error: 'Resource not found' });
    }

    await resource.remove();
    res.json({ message: 'Resource deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Error deleting resource' });
  }
};

// Toggle resource featured status
exports.toggleResourceFeatured = async (req, res) => {
  try {
    const resource = await Resource.findById(req.params.id);
    if (!resource) {
      return res.status(404).json({ error: 'Resource not found' });
    }

    resource.featured = !resource.featured;
    await resource.save();

    res.json({
      message: `Resource ${resource.featured ? 'featured' : 'unfeatured'} successfully`,
      featured: resource.featured
    });
  } catch (error) {
    res.status(500).json({ error: 'Error toggling resource featured status' });
  }
};

// Bulk action on resources
exports.bulkActionResources = async (req, res) => {
  try {
    const { action, resourceIds } = req.body;

    switch (action) {
      case 'delete':
        await Resource.deleteMany({ _id: { $in: resourceIds } });
        req.flash('success', 'Selected resources deleted successfully');
        break;

      case 'feature':
        await Resource.updateMany(
          { _id: { $in: resourceIds } },
          { $set: { featured: true } }
        );
        req.flash('success', 'Selected resources featured successfully');
        break;

      case 'unfeature':
        await Resource.updateMany(
          { _id: { $in: resourceIds } },
          { $set: { featured: false } }
        );
        req.flash('success', 'Selected resources unfeatured successfully');
        break;

      case 'publish':
        await Resource.updateMany(
          { _id: { $in: resourceIds } },
          { $set: { status: 'published' } }
        );
        req.flash('success', 'Selected resources published successfully');
        break;

      case 'unpublish':
        await Resource.updateMany(
          { _id: { $in: resourceIds } },
          { $set: { status: 'draft' } }
        );
        req.flash('success', 'Selected resources unpublished successfully');
        break;

      default:
        req.flash('error', 'Invalid bulk action');
    }

    res.redirect('/admin/resources');
  } catch (error) {
    req.flash('error', 'Error performing bulk action');
    res.redirect('/admin/resources');
  }
};
