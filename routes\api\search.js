const express = require('express');
const router = express.Router();
const Project = require('../../models/Project');
const Event = require('../../models/Event');
const Resource = require('../../models/Resource');
const ApiResponse = require('../../utils/apiResponse');
const { searchLimiter } = require('../../middleware/rateLimiter');
const { validationChains, handleValidationErrors } = require('../../middleware/apiValidation');

// Apply search rate limiting
router.use(searchLimiter);

/**
 * @route   GET /api/search
 * @desc    Search across all content types
 * @access  Public
 */
router.get('/',
  validationChains.search,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { q: query, type, page = 1, limit = 10 } = req.query;

      if (!query || query.trim().length < 2) {
        return ApiResponse.success(res, {
          results: [],
          total: 0,
          query: query || '',
          searchTypes: ['all', 'projects', 'events', 'resources']
        }, 'Search query too short');
      }

      const searchRegex = new RegExp(query.trim(), 'i');
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      let results = [];
      let total = 0;

      // Search in different content types based on type parameter
      if (!type || type === 'all' || type === 'projects') {
        const projects = await Project.find({
          $or: [
            { title: searchRegex },
            { description: searchRegex },
            { authors: { $in: [searchRegex] } },
            { tags: { $in: [searchRegex] } }
          ]
        }).limit(type === 'projects' ? limitNum : 25);

        projects.forEach(project => {
          results.push({
            type: 'project',
            id: project._id,
            title: project.title,
            description: project.description.substring(0, 200) + '...',
            url: `/projects/${project._id}`,
            image: project.image,
            authors: project.authors,
            tags: project.tags,
            featured: project.featured,
            createdAt: project.createdAt
          });
        });
      }



      if (!type || type === 'all' || type === 'events') {
        const events = await Event.find({
          $or: [
            { title: searchRegex },
            { description: searchRegex },
            { location: searchRegex }
          ]
        }).limit(type === 'events' ? limitNum : 25);

        events.forEach(event => {
          results.push({
            type: 'event',
            id: event._id,
            title: event.title,
            description: event.description.substring(0, 200) + '...',
            url: `/events/${event._id}`,
            image: event.image,
            date: event.date,
            location: event.location,
            isPast: event.isPast,
            createdAt: event.createdAt
          });
        });
      }

      if (!type || type === 'all' || type === 'resources') {
        const resources = await Resource.find({
          $or: [
            { title: searchRegex },
            { description: searchRegex },
            { category: searchRegex },
            { tags: { $in: [searchRegex] } }
          ]
        }).limit(type === 'resources' ? limitNum : 25);

        resources.forEach(resource => {
          results.push({
            type: 'resource',
            id: resource._id,
            title: resource.title,
            description: resource.description.substring(0, 200) + '...',
            url: `/resources/${resource._id}`,
            category: resource.category,
            tags: resource.tags,
            resourceType: resource.resourceType,
            fileUrl: resource.fileUrl,
            externalUrl: resource.externalUrl,
            createdAt: resource.createdAt
          });
        });
      }

      // Sort results by relevance (title matches first, then by date)
      results.sort((a, b) => {
        const aTitle = a.title.toLowerCase().includes(query.toLowerCase());
        const bTitle = b.title.toLowerCase().includes(query.toLowerCase());

        if (aTitle && !bTitle) return -1;
        if (!aTitle && bTitle) return 1;

        return new Date(b.createdAt) - new Date(a.createdAt);
      });

      total = results.length;

      // Apply pagination
      if (type && type !== 'all') {
        // For specific type searches, pagination is already handled in the query
        results = results.slice(0, limitNum);
      } else {
        // For 'all' searches, apply pagination to combined results
        results = results.slice(skip, skip + limitNum);
      }

      const totalPages = Math.ceil(total / limitNum);

      const pagination = {
        currentPage: pageNum,
        totalPages,
        totalItems: total,
        itemsPerPage: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      };

      ApiResponse.paginated(res, results, pagination, `Found ${total} results for "${query}"`);

    } catch (error) {
      console.error('Search error:', error);
      ApiResponse.error(res, 'Search failed');
    }
  }
);

/**
 * @route   GET /api/search/suggestions
 * @desc    Get search suggestions
 * @access  Public
 */
router.get('/suggestions', async (req, res) => {
  try {
    const { q: query } = req.query;

    if (!query || query.trim().length < 2) {
      return ApiResponse.success(res, [], 'Query too short for suggestions');
    }

    const searchRegex = new RegExp(query.trim(), 'i');
    const suggestions = [];

    // Get title suggestions from different content types
    const [projects, events, resources] = await Promise.all([
      Project.find({ title: searchRegex }).select('title').limit(5),
      Event.find({ title: searchRegex }).select('title').limit(5),
      Resource.find({ title: searchRegex }).select('title').limit(5)
    ]);

    // Add suggestions with type information
    projects.forEach(p => suggestions.push({ text: p.title, type: 'project' }));
    events.forEach(e => suggestions.push({ text: e.title, type: 'event' }));
    resources.forEach(r => suggestions.push({ text: r.title, type: 'resource' }));

    // Remove duplicates and limit to 10
    const uniqueSuggestions = suggestions
      .filter((suggestion, index, self) =>
        index === self.findIndex(s => s.text === suggestion.text)
      )
      .slice(0, 10);

    ApiResponse.success(res, uniqueSuggestions, 'Search suggestions retrieved');

  } catch (error) {
    console.error('Search suggestions error:', error);
    ApiResponse.error(res, 'Failed to get search suggestions');
  }
});

/**
 * @route   GET /api/search/popular
 * @desc    Get popular search terms (mock data for now)
 * @access  Public
 */
router.get('/popular', (req, res) => {
  try {
    // This would typically come from a search analytics database
    const popularTerms = [
      'machine learning',
      'climate change',
      'quantum physics',
      'biotechnology',
      'artificial intelligence',
      'renewable energy',
      'space exploration',
      'genetics',
      'robotics',
      'nanotechnology'
    ];

    ApiResponse.success(res, popularTerms, 'Popular search terms retrieved');
  } catch (error) {
    console.error('Popular search terms error:', error);
    ApiResponse.error(res, 'Failed to get popular search terms');
  }
});

/**
 * @route   GET /api/search/filters
 * @desc    Get available search filters (categories, tags, authors)
 * @access  Public
 */
router.get('/filters', async (req, res) => {
  try {
    const [resourceCategories, projectTags, resourceTags, projectAuthors] = await Promise.all([
      Resource.distinct('category'),
      Project.distinct('tags'),
      Resource.distinct('tags'),
      Project.distinct('authors')
    ]);

    const filters = {
      categories: [...new Set([...resourceCategories])].filter(cat => cat && cat.trim().length > 0),
      tags: [...new Set([...projectTags.flat(), ...resourceTags.flat()])].filter(tag => tag && tag.trim().length > 0),
      authors: [...new Set([...projectAuthors.flat()])].filter(author => author && author.trim().length > 0)
    };

    ApiResponse.success(res, filters, 'Search filters retrieved successfully');
  } catch (error) {
    console.error('Search filters error:', error);
    ApiResponse.error(res, 'Failed to get search filters');
  }
});

/**
 * @route   POST /api/search/analytics
 * @desc    Track search analytics (for future implementation)
 * @access  Public
 */
router.post('/analytics', (req, res) => {
  try {
    const { query, type, resultsCount, clickedResult } = req.body;

    // In a real implementation, this would save to a search analytics database
    console.log('Search Analytics:', {
      query,
      type,
      resultsCount,
      clickedResult,
      timestamp: new Date(),
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    ApiResponse.success(res, null, 'Search analytics recorded');
  } catch (error) {
    console.error('Search analytics error:', error);
    ApiResponse.error(res, 'Failed to record search analytics');
  }
});

module.exports = router;
