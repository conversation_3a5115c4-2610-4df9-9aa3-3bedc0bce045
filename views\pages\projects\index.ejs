<!-- Projects Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <img src="/images/logo.jpg" alt="Science Club Turbat Logo" class="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 object-contain rounded-lg shadow-lg">
            </div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Research Projects</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Discover innovative research projects led by our talented student researchers
            </p>
        </div>
    </div>
</section>


<!-- Projects Grid -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <% if (projects && projects.length > 0) { %>
            <div class="grid-3" id="projects-grid">
                <% projects.forEach(project => { %>
                    <div class="project-card" data-tags="<%= project.tags ? project.tags.join(',') : '' %>">
                        <% if (project.image) { %>
                            <img src="<%= project.image %>" alt="<%= project.title %>" class="project-image">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <i class="fas fa-flask text-white text-4xl"></i>
                            </div>
                        <% } %>

                        <div class="project-content">
                            <% if (project.featured) { %>
                                <div class="mb-2">
                                   <span class="inline-flex items-center gap-1  fea text-xs font-semibold px-3 p-2 rounded-full shadow-sm">
                                     <i class="fas fa-star text-yellow-500"></i> Featured
                                    </span>

                                </div>
                            <% } %>

                            <h3 class="project-title"><%= project.title %></h3>
                            <p class="project-description">
                                <%= project.description.length > 150 ? project.description.substring(0, 150) + '...' : project.description %>
                            </p>

                            <% if (project.authors && project.authors.length > 0) { %>
                                <div class="project-authors">
                                    <i class="fas fa-user mr-1"></i>
                                    <%= project.authors.join(', ') %>
                                </div>
                            <% } %>

                            <% if (project.tags && project.tags.length > 0) { %>
                                <div class="categories">
                                    <% project.tags.slice(0, 3).forEach(tag => { %>
                                        <span class="category"><%= tag %></span>
                                    <% }) %>
                                    <% if (project.tags.length > 3) { %>
                                        <span class="category">+<%= project.tags.length - 3 %> more</span>
                                    <% } %>
                                </div>
                            <% } %>

                            <div class="mt-6 flex items-center justify-between">
                                <a href="/projects/<%= project._id %>" class="btn-primary">
                                    <i class="fas fa-arrow-right mr-1"></i>Learn More
                                </a>

                                <% if (project.pdfFile) { %>
                                    <a href="<%= project.pdfFile %>" target="_blank" class="text-red-600 hover:text-red-800" title="Download PDF">
                                        <i class="fas fa-file-pdf text-xl"></i>
                                    </a>
                                <% } %>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-16">
                <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-flask text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Projects Found</h3>
                <p class="text-gray-600 mb-8">
                    We don't have any research projects to display at the moment.
                    Check back soon for exciting new research!
                </p>
                <a href="/contact" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Submit Your Project
                </a>
            </div>
        <% } %>
    </div>
</section>





<!-- Call to Action -->
<section class="py-12 sm:py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Have a Research Project?</h2>
        <p class="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 leading-relaxed">
            Share your research with our community and get feedback from fellow researchers.
        </p>
        <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
            <a href="/contact" class="btn btn-primary btn-lg w-full sm:w-auto">
               Submit Project
            </a>
            <a href="/about" class="btn btn-outline btn-lg w-full sm:w-auto">
              Learn More
            </a>
        </div>
    </div>
</section>


