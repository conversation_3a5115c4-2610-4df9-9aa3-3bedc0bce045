#!/usr/bin/env node

/**
 * Production Verification Script
 * Verifies that the production deployment is working correctly
 */

const https = require('https');
const http = require('http');

const PRODUCTION_URL = 'https://scienceclub.onrender.com';

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            error: 'Invalid JSON response'
          });
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

async function verifyProduction() {
  console.log('🔍 Verifying Production Deployment...\n');
  
  try {
    // Test 1: API Health Check
    console.log('1. Testing API Health...');
    const healthResponse = await makeRequest(`${PRODUCTION_URL}/api/health`);
    console.log(`   Status: ${healthResponse.status}`);
    if (healthResponse.status === 200 && healthResponse.data.success) {
      console.log('   ✅ API Health: PASS');
      console.log(`   📊 Environment: ${healthResponse.data.data.environment}`);
    } else {
      console.log('   ❌ API Health: FAIL');
      console.log('   📊 Response:', healthResponse.data);
    }
    
    // Test 2: Debug Endpoint
    console.log('\n2. Testing Debug Endpoint...');
    const debugResponse = await makeRequest(`${PRODUCTION_URL}/api/debug`);
    console.log(`   Status: ${debugResponse.status}`);
    if (debugResponse.status === 200 && debugResponse.data.success) {
      console.log('   ✅ Debug Endpoint: PASS');
      const data = debugResponse.data.data;
      console.log(`   📊 Database: ${data.database.connected ? 'Connected' : 'Disconnected'}`);
      console.log(`   📊 Published Blogs: ${data.counts.publishedBlogs}`);
      console.log(`   📊 Featured Projects: ${data.counts.featuredProjects}`);
      console.log(`   📊 Total Events: ${data.counts.totalEvents}`);
    } else {
      console.log('   ❌ Debug Endpoint: FAIL');
      console.log('   📊 Response:', debugResponse.data);
    }
    
    // Test 3: Blog API
    console.log('\n3. Testing Blog API...');
    const blogResponse = await makeRequest(`${PRODUCTION_URL}/api/blog`);
    console.log(`   Status: ${blogResponse.status}`);
    if (blogResponse.status === 200 && blogResponse.data.success) {
      console.log('   ✅ Blog API: PASS');
      const blogs = blogResponse.data.data || [];
      console.log(`   📊 Found ${blogs.length} published blogs`);
    } else {
      console.log('   ❌ Blog API: FAIL');
      console.log('   📊 Response:', blogResponse.data);
    }
    
    // Test 4: Projects API
    console.log('\n4. Testing Projects API...');
    const projectsResponse = await makeRequest(`${PRODUCTION_URL}/api/projects`);
    console.log(`   Status: ${projectsResponse.status}`);
    if (projectsResponse.status === 200 && projectsResponse.data.success) {
      console.log('   ✅ Projects API: PASS');
      const projects = projectsResponse.data.data || [];
      console.log(`   📊 Found ${projects.length} projects`);
    } else {
      console.log('   ❌ Projects API: FAIL');
      console.log('   📊 Response:', projectsResponse.data);
    }
    
    // Test 5: Events API
    console.log('\n5. Testing Events API...');
    const eventsResponse = await makeRequest(`${PRODUCTION_URL}/api/events`);
    console.log(`   Status: ${eventsResponse.status}`);
    if (eventsResponse.status === 200 && eventsResponse.data.success) {
      console.log('   ✅ Events API: PASS');
      const events = eventsResponse.data.data || [];
      console.log(`   📊 Found ${events.length} events`);
    } else {
      console.log('   ❌ Events API: FAIL');
      console.log('   📊 Response:', eventsResponse.data);
    }
    
    console.log('\n🎉 Production Verification Complete!');
    
  } catch (error) {
    console.error('\n❌ Verification Failed:', error.message);
  }
}

// Run verification
verifyProduction();
