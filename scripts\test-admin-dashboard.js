#!/usr/bin/env node

/**
 * Test Admin Dashboard
 * Tests the admin dashboard functionality
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function testAdminDashboard() {
  const baseURL = process.env.BASE_URL || 'http://localhost:10000';
  
  console.log('Testing Admin Dashboard...');
  console.log(`Base URL: ${baseURL}`);
  
  try {
    // Test admin login page
    console.log('\n1. Testing admin login page...');
    const loginResponse = await axios.get(`${baseURL}/admin/login`);
    console.log(`✓ Admin login page: ${loginResponse.status}`);
    
    // Test API health
    console.log('\n2. Testing API health...');
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    console.log(`✓ API health: ${healthResponse.status}`);
    console.log(`API Status: ${healthResponse.data.data.status}`);
    
    // Test public API endpoints
    console.log('\n3. Testing public API endpoints...');
    
    const projectsResponse = await axios.get(`${baseURL}/api/projects`);
    console.log(`✓ Projects API: ${projectsResponse.status}`);
    
    const blogsResponse = await axios.get(`${baseURL}/api/blog`);
    console.log(`✓ Blog API: ${blogsResponse.status}`);
    
    const eventsResponse = await axios.get(`${baseURL}/api/events`);
    console.log(`✓ Events API: ${eventsResponse.status}`);
    
    const resourcesResponse = await axios.get(`${baseURL}/api/resources`);
    console.log(`✓ Resources API: ${resourcesResponse.status}`);
    
    // Test contact form submission
    console.log('\n4. Testing contact form submission...');
    const contactData = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Message',
      message: 'This is a test message from the admin dashboard test script.'
    };
    
    const contactResponse = await axios.post(`${baseURL}/api/contacts`, contactData, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log(`✓ Contact submission: ${contactResponse.status}`);
    
    console.log('\n🎉 All tests passed! Admin dashboard should be working correctly.');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data:`, error.response.data);
    }
    process.exit(1);
  }
}

// Run the test
testAdminDashboard();
