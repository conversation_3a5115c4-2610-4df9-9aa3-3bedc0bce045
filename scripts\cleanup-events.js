const mongoose = require('mongoose');
const Event = require('../models/Event');
require('dotenv').config();

async function cleanupEvents() {
  try {
    console.log('🧹 Starting cleanup of existing events...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scienceclub');
    console.log('✅ Connected to MongoDB');
    
    // Get count of existing events
    const existingCount = await Event.countDocuments();
    console.log(`📊 Found ${existingCount} existing events`);
    
    if (existingCount > 0) {
      // Delete all existing events
      const result = await Event.deleteMany({});
      console.log(`🗑️ Deleted ${result.deletedCount} events`);
    } else {
      console.log('ℹ️ No events to delete');
    }
    
    console.log('✅ Cleanup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run cleanup if called directly
if (require.main === module) {
  cleanupEvents();
}

module.exports = cleanupEvents;
