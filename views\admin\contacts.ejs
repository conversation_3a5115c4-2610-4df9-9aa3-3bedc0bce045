<%- contentFor('title') %>
<%= title %>
<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Contact Messages</h1>
            <p class="text-gray-600 mt-2">Manage and respond to contact form submissions</p>
        </div>
        <div class="text-sm text-gray-500">
            Total: <%= total %> messages
        </div>
    </div>

    <!-- Flash Messages -->
    <% if (typeof success !== 'undefined' && success.length > 0) { %>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
            <% success.forEach(function(msg) { %>
                <p><%= msg %></p>
            <% }); %>
        </div>
    <% } %>

    <% if (typeof error !== 'undefined' && error.length > 0) { %>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            <% error.forEach(function(msg) { %>
                <p><%= msg %></p>
            <% }); %>
        </div>
    <% } %>

    <!-- Contacts Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <% if (contacts.length > 0) { %>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Subject
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% contacts.forEach(contact => { %>
                        <tr class="<%= contact.isRead ? 'bg-gray-50' : 'bg-white' %>">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 <%= !contact.isRead ? 'font-bold' : '' %>">
                                            <%= contact.name %>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <%= contact.email %>
                                        </div>
                                        <% if (contact.phone) { %>
                                            <div class="text-sm text-gray-500">
                                                <%= contact.phone %>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 <%= !contact.isRead ? 'font-bold' : '' %>">
                                    <%= contact.subject %>
                                </div>
                                <div class="text-sm text-gray-500 truncate max-w-xs">
                                    <%= contact.message.substring(0, 100) %><%= contact.message.length > 100 ? '...' : '' %>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= new Date(contact.createdAt).toLocaleDateString() %>
                                <br>
                                <%= new Date(contact.createdAt).toLocaleTimeString() %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (contact.isRead) { %>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Read
                                    </span>
                                <% } else { %>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-envelope mr-1"></i>
                                        Unread
                                    </span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a 
                                        href="/admin/contacts/<%= contact._id %>" 
                                        class="text-blue-600 hover:text-blue-900"
                                        title="View"
                                    >
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button 
                                        onclick="toggleRead('<%= contact._id %>', <%= contact.isRead %>)"
                                        class="text-yellow-600 hover:text-yellow-900"
                                        title="<%= contact.isRead ? 'Mark as Unread' : 'Mark as Read' %>"
                                    >
                                        <i class="fas fa-<%= contact.isRead ? 'envelope' : 'envelope-open' %>"></i>
                                    </button>
                                    <form 
                                        action="/admin/contacts/<%= contact._id %>/delete" 
                                        method="POST" 
                                        class="inline"
                                        onsubmit="return confirm('Are you sure you want to delete this message?')"
                                    >
                                        <button 
                                            type="submit" 
                                            class="text-red-600 hover:text-red-900"
                                            title="Delete"
                                        >
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>

            <!-- Pagination -->
            <% if (totalPages > 1) { %>
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <% if (currentPage > 1) { %>
                            <a href="?page=<%= currentPage - 1 %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        <% } %>
                        <% if (currentPage < totalPages) { %>
                            <a href="?page=<%= currentPage + 1 %>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        <% } %>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing page <span class="font-medium"><%= currentPage %></span> of <span class="font-medium"><%= totalPages %></span>
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                <% for (let i = 1; i <= totalPages; i++) { %>
                                    <a 
                                        href="?page=<%= i %>" 
                                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <%= i === currentPage ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' %>"
                                    >
                                        <%= i %>
                                    </a>
                                <% } %>
                            </nav>
                        </div>
                    </div>
                </div>
            <% } %>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-inbox text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No contact messages</h3>
                <p class="text-gray-500">No one has submitted the contact form yet.</p>
            </div>
        <% } %>
    </div>
</div>

<script>
function toggleRead(contactId, isCurrentlyRead) {
    fetch(`/admin/contacts/${contactId}/toggle-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status');
    });
}
</script>
