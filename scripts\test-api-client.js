#!/usr/bin/env node

/**
 * Test API Client Script
 * Tests the internal API client to ensure it's working correctly
 */

const dotenv = require('dotenv');
const apiClient = require('../utils/apiClient');

// Load environment variables
dotenv.config();

async function testApiClient() {
  console.log('🧪 Testing API Client...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing API Health Check...');
    const healthResponse = await fetch(`http://localhost:${process.env.PORT || 3000}/api/health`);
    const healthData = await healthResponse.json();
    console.log('   ✅ Health Check:', healthData.success ? 'PASS' : 'FAIL');
    console.log('   📊 Status:', healthData.data?.status || 'Unknown');



    // Test 3: Get Featured Projects
    console.log('\n3. Testing Featured Projects...');
    const projectsResponse = await apiClient.getFeaturedProjects();
    console.log('   ✅ Projects API:', projectsResponse.success ? 'PASS' : 'FAIL');
    console.log('   📊 Message:', projectsResponse.message);
    if (projectsResponse.success) {
      const projects = projectsResponse.data.projects || projectsResponse.data || [];
      console.log('   🚀 Found', projects.length, 'featured projects');
      if (projects.length > 0) {
        console.log('   📄 First project:', projects[0].title);
      }
    } else {
      console.log('   ❌ Error:', projectsResponse.message);
    }

    // Test 4: Get Upcoming Events
    console.log('\n4. Testing Upcoming Events...');
    const eventsResponse = await apiClient.getUpcomingEvents();
    console.log('   ✅ Events API:', eventsResponse.success ? 'PASS' : 'FAIL');
    console.log('   📊 Message:', eventsResponse.message);
    if (eventsResponse.success) {
      const events = eventsResponse.data.events || eventsResponse.data || [];
      console.log('   📅 Found', events.length, 'upcoming events');
      if (events.length > 0) {
        console.log('   📄 First event:', events[0].title);
      }
    } else {
      console.log('   ❌ Error:', eventsResponse.message);
    }

    console.log('\n🎉 API Client Test Complete!');

  } catch (error) {
    console.error('\n❌ Test Failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testApiClient().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Test script error:', error);
    process.exit(1);
  });
}

module.exports = testApiClient;
